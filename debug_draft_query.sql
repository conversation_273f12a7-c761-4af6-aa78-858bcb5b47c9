-- Query to examine the specific draft causing validation errors
-- Draft public ID: e9fab30f-479e-466f-ac46-1af2782be960

SELECT 
    de.id,
    de.public_id,
    de.exercise_type,
    de.status,
    de.difficulty,
    de.created_at,
    de.updated_at,
    -- Pretty print the JSON data for easier reading
    jsonb_pretty(de.data_json) as data_json_formatted,
    jsonb_pretty(de.solution_json) as solution_json_formatted,
    -- Check if correct_answer exists in solution_json
    CASE 
        WHEN de.solution_json ? 'correct_answer' THEN 'EXISTS'
        ELSE 'MISSING'
    END as correct_answer_status,
    -- Extract just the correct_answer field if it exists
    de.solution_json->'correct_answer' as correct_answer_value,
    -- Check the structure of solution_json
    jsonb_object_keys(de.solution_json) as solution_json_keys,
    -- Learning node info
    ln.title as learning_node_title,
    ln.public_id as learning_node_public_id
FROM draft_exercises de
LEFT JOIN learning_nodes ln ON de.learning_node_id = ln.id
WHERE de.public_id = 'e9fab30f-479e-466f-ac46-1af2782be960';

-- Additional query to check if there are other drafts with similar issues
SELECT 
    COUNT(*) as total_drafts,
    COUNT(CASE WHEN solution_json ? 'correct_answer' THEN 1 END) as drafts_with_correct_answer,
    COUNT(CASE WHEN solution_json ? 'correct_answer' THEN NULL ELSE 1 END) as drafts_missing_correct_answer,
    exercise_type
FROM draft_exercises 
WHERE solution_json IS NOT NULL
GROUP BY exercise_type
ORDER BY exercise_type;

-- Query to see the exact structure of solution_json for highlight exercises
SELECT 
    public_id,
    exercise_type,
    jsonb_pretty(solution_json) as solution_structure,
    CASE 
        WHEN solution_json ? 'correct_answer' THEN 'HAS_CORRECT_ANSWER'
        ELSE 'MISSING_CORRECT_ANSWER'
    END as status
FROM draft_exercises 
WHERE exercise_type = 'highlight' 
AND solution_json IS NOT NULL
LIMIT 5;
