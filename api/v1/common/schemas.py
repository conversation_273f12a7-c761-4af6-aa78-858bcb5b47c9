from pydantic import <PERSON>Mode<PERSON>, Field, ConfigDict
from typing import Optional, List, Dict, Any, Union
import enum

class AppErrorCode(str, enum.Enum):
    # General Errors
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
    SERVICE_ERROR = "SERVICE_ERROR" 
    INVALID_REQUEST = "INVALID_REQUEST" 
    PERMISSION_DENIED = "PERMISSION_DENIED"
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND" 
    EXTERNAL_SERVICE_FAILURE = "EXTERNAL_SERVICE_FAILURE" 
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE" 
    BAD_REQUEST = "BAD_REQUEST"
    TOO_MANY_REQUESTS = "TOO_MANY_REQUESTS"

    # Authentication & Authorization Errors
    AUTHENTICATION_REQUIRED = "AUTHENTICATION_REQUIRED" 
    INVALID_TOKEN = "INVALID_TOKEN" 
    INVALID_CREDENTIALS = "INVALID_CREDENTIALS" 
    ACCOUNT_LOCKED = "ACCOUNT_LOCKED"
    EMAIL_NOT_VERIFIED = "EMAIL_NOT_VERIFIED"
    PARENT_ACCOUNT_NOT_FOUND = "PARENT_ACCOUNT_NOT_FOUND" 
    CHILD_ACCOUNT_NOT_FOUND = "CHILD_ACCOUNT_NOT_FOUND" 
    USER_NOT_FOUND = "USER_NOT_FOUND" 
    AUTHORIZATION_ERROR = "AUTHORIZATION_ERROR"
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR"
    ACCOUNT_NOT_VERIFIED = "ACCOUNT_NOT_VERIFIED"
    ACCOUNT_MIGRATION_REQUIRED = "ACCOUNT_MIGRATION_REQUIRED" 
    ASSIGNMENT_NOT_FOUND_OR_INVALID_CODE = "ASSIGNMENT_NOT_FOUND_OR_INVALID_CODE"
    ACCOUNT_ALREADY_VERIFIED = "ACCOUNT_ALREADY_VERIFIED"
    INVALID_VERIFICATION_CODE = "INVALID_VERIFICATION_CODE"
    EXPIRED_VERIFICATION_CODE = "EXPIRED_VERIFICATION_CODE"
    INVALID_OR_EXPIRED_TOKEN = "INVALID_OR_EXPIRED_TOKEN"
    TOKEN_EXPIRED = "TOKEN_EXPIRED"

    # Account Management Errors
    ACCOUNT_ALREADY_EXISTS = "ACCOUNT_ALREADY_EXISTS"
    EMAIL_ALREADY_EXISTS = "EMAIL_ALREADY_EXISTS" 
    MAX_CHILD_ACCOUNTS_REACHED = "MAX_CHILD_ACCOUNTS_REACHED"
    INVALID_PIN_FORMAT = "INVALID_PIN_FORMAT"
    PIN_RESET_TOKEN_INVALID = "PIN_RESET_TOKEN_INVALID"
    PIN_RESET_TOKEN_EXPIRED = "PIN_RESET_TOKEN_EXPIRED"
    CHILD_ALREADY_LINKED = "CHILD_ALREADY_LINKED"
    CHILD_ALREADY_HAS_PARENT = "CHILD_ALREADY_HAS_PARENT"

    # Subscription & Payment Errors
    STRIPE_ERROR = "STRIPE_ERROR" 
    STRIPE_CUSTOMER_ID_NOT_FOUND = "STRIPE_CUSTOMER_ID_NOT_FOUND"
    SUBSCRIPTION_NOT_FOUND = "SUBSCRIPTION_NOT_FOUND"
    ACTIVE_SUBSCRIPTION_EXISTS = "ACTIVE_SUBSCRIPTION_EXISTS"
    SUBSCRIPTION_PAUSE_NOT_FOUND = "SUBSCRIPTION_PAUSE_NOT_FOUND"
    INVALID_DISCOUNT_CODE = "INVALID_DISCOUNT_CODE"
    EXPIRED_DISCOUNT_CODE = "EXPIRED_DISCOUNT_CODE"
    INVALID_DISCOUNT_FOR_TYPE = "INVALID_DISCOUNT_FOR_TYPE" 
    BILLING_PERIOD_MISMATCH = "BILLING_PERIOD_MISMATCH"
    PAYMENT_FAILED = "PAYMENT_FAILED"
    PLAN_NOT_FOUND = "PLAN_NOT_FOUND" 
    OFFERING_PRICE_NOT_FOUND = "OFFERING_PRICE_NOT_FOUND" 
    
    # Content/Exercise Errors
    EXERCISE_NOT_FOUND = "EXERCISE_NOT_FOUND"
    LEARNING_NODE_NOT_FOUND = "LEARNING_NODE_NOT_FOUND"
    INVALID_ANSWER_FORMAT = "INVALID_ANSWER_FORMAT"
    SCHOOL_YEAR_NOT_FOUND = "SCHOOL_YEAR_NOT_FOUND"
    SUBJECT_NOT_FOUND = "SUBJECT_NOT_FOUND"

    # Other specific errors
    EMAIL_SEND_FAILURE = "EMAIL_SEND_FAILURE"
    CHILD_ACCOUNT_ALREADY_EXISTS = "CHILD_ACCOUNT_ALREADY_EXISTS"
    PARENT_ACCOUNT_ALREADY_EXISTS = "PARENT_ACCOUNT_ALREADY_EXISTS"
    
    # Editorial specific errors
    NOT_ASSIGNED = "NOT_ASSIGNED"
    INVALID_STATUS = "INVALID_STATUS"
    DRAFT_REJECTED = "DRAFT_REJECTED"
    OUT_OF_SCOPE = "OUT_OF_SCOPE"

    # ... add more specific error codes as needed

class ErrorDetail(BaseModel):
    field: Optional[str] = None 
    code: Optional[str] = None   
    message: str                 

class SimpleErrorResponse(BaseModel):
    status: str = "error"
    error_code: Union[AppErrorCode, str] = Field(..., description="Application-specific error code.")
    message: str = Field(..., description="User-friendly error message.")
    details: Optional[List[ErrorDetail]] = Field(None, description="Optional list of detailed field errors.")
    request_id: Optional[str] = Field(None, description="Unique ID for tracing this request.")

    model_config = ConfigDict(
        use_enum_values=True 
    )

class SuccessResponse(BaseModel):
    status: str = "success"
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
