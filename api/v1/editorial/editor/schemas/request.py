from pydantic import BaseModel, Field, field_validator
from typing import Optional, Dict, Any, List
from db.models import DraftExerciseStatus

class DraftListQueryParams(BaseModel):
    status: Optional[DraftExerciseStatus] = Field(None, description="Filter by draft status")
    subject_id: Optional[str] = Field(None, description="Filter by subject public ID")
    chapter_id: Optional[str] = Field(None, description="Filter by chapter public ID")
    learning_node_id: Optional[str] = Field(None, description="Filter by learning node public ID")
    page: int = Field(1, ge=1, description="Page number")
    page_size: int = Field(50, ge=1, le=100, description="Items per page")

class DraftUpdateRequest(BaseModel):
    data_json: Dict[str, Any] = Field(..., description="Exercise data JSON")
    solution_json: Optional[Dict[str, Any]] = Field(None, description="Exercise solution JSON")
    
    @field_validator('data_json')
    @classmethod
    def validate_data_not_empty(cls, v: Dict[str, Any]) -> Dict[str, Any]:
        if not v:
            raise ValueError("Exercise data cannot be empty")
        if 'prompt' not in v:
            raise ValueError("Exercise data must contain a prompt")
        return v
    
    @field_validator('solution_json')
    @classmethod
    def validate_solution_format(cls, v: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        if v is not None and 'correct_answer' not in v:
            raise ValueError("Solution must contain correct_answer field")
        return v

class DraftClaimRequest(BaseModel):
    """Empty body for claim request - draft ID comes from URL"""
    pass

class DraftAcceptRequest(BaseModel):
    """Empty body for accept request - draft ID comes from URL"""
    pass

class DraftReleaseRequest(BaseModel):
    reason: Optional[str] = Field(None, max_length=500, description="Reason for releasing the draft")

class DraftCreateRequest(BaseModel):
    exercise_type: str = Field(..., description="Type of exercise")
    difficulty: str = Field(..., description="Difficulty level")
    data_json: Dict[str, Any] = Field(..., description="Exercise data JSON")
    solution_json: Dict[str, Any] = Field(..., description="Exercise solution JSON")
    learning_node_ids: List[str] = Field(..., description="Learning node public IDs to associate with the draft")

    @field_validator('exercise_type')
    @classmethod
    def validate_exercise_type(cls, v: str) -> str:
        if not v:
            raise ValueError("Exercise type cannot be empty")
        return v

    @field_validator('difficulty')
    @classmethod
    def validate_difficulty(cls, v: str) -> str:
        if v not in ['low', 'medium', 'high']:
            raise ValueError("Difficulty must be one of: low, medium, high")
        return v

    @field_validator('data_json')
    @classmethod
    def validate_data_not_empty(cls, v: Dict[str, Any]) -> Dict[str, Any]:
        if not v:
            raise ValueError("Exercise data cannot be empty")
        return v

    @field_validator('solution_json')
    @classmethod
    def validate_solution_not_empty(cls, v: Dict[str, Any]) -> Dict[str, Any]:
        if not v:
            raise ValueError("Exercise solution cannot be empty")
        return v

    @field_validator('learning_node_ids')
    @classmethod
    def validate_learning_node_ids(cls, v: List[str]) -> List[str]:
        if not v:
            raise ValueError("At least one learning node ID is required")
        return v

class DraftRejectRequest(BaseModel):
    rejection_reason: str = Field(..., max_length=500, description="Reason for rejecting the draft")
    rejection_type: Optional[str] = Field(None, description="Type of rejection issue (quality/irrelevant/duplicate/other)")
    
    @field_validator('rejection_type')
    @classmethod
    def validate_rejection_type(cls, v: Optional[str]) -> Optional[str]:
        if v and v not in ['quality', 'irrelevant', 'duplicate', 'other']:
            raise ValueError("Rejection type must be one of: quality, irrelevant, duplicate, other")
        return v