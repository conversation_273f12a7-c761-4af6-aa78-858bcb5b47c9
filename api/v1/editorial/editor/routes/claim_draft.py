from fastapi import APIRouter, Depends, Path, HTTPException, status
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireEditor, EditorAuthResponse
)
from api.v1.editorial.editor.schemas.response import DraftActionResponse
from services.draft_management.draft_service import DraftManagementService
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, PermissionDeniedError, ValidationError
)
from loguru import logger

router = APIRouter()

@router.post(
    "/drafts/{draft_id}/claim",
    response_model=DraftActionResponse,
    status_code=status.HTTP_200_OK,
    summary="Claim Draft",
    description="Claim a draft for editing"
)
async def claim_draft(
    draft_id: int = Path(..., description="Draft ID"),
    auth: EditorAuthResponse = Depends(RequireEditor),
    db: Session = Depends(get_db)
):
    """
    Claim a draft for editing.
    
    Draft must be in NEW or REJECTED_BY_ADMIN status.
    Editor must have scope to access the draft.
    Uses single-owner model - only one editor can work on a draft at a time.
    """
    try:
        # Claim the draft
        draft = await DraftManagementService.claim_draft(
            db=db,
            draft_id=draft_id,
            editor=auth.editor
        )
        
        # Get updated draft details for response
        from .draft_detail import get_draft_detail
        detail_response = await get_draft_detail(draft.public_id, auth, db)
        
        return DraftActionResponse(
            success=True,
            message=f"Draft claimed successfully. Status changed to IN_REVIEW.",
            draft=detail_response
        )
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.message
        )
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=e.message
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Error claiming draft: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while claiming the draft"
        )