# Draft Exercise Relationship Simplification Migration Guide

## Overview
This migration simplifies the draft exercise to learning node relationship from a many-to-many (via linking table) to a direct foreign key relationship.

## Files
- `005_simplify_draft_exercise_relationships.sql` - Main migration script
- `005_simplify_draft_exercise_relationships_rollback.sql` - Rollback script

## Pre-Migration Checklist

1. **Backup your database** (always recommended before major schema changes)
2. **Stop your application** to prevent new data during migration
3. **Verify you have existing data** to migrate:
   ```sql
   SELECT COUNT(*) FROM draft_exercise;
   SELECT COUNT(*) FROM draft_learning_node_exercise;
   ```

## Execution Steps

### 1. Run the Migration
Connect to your PostgreSQL database and execute:
```bash
psql -U your_username -d your_database -f migrations/005_simplify_draft_exercise_relationships.sql
```

Or copy and paste the SQL content directly into your PostgreSQL client.

### 2. Review Migration Output
The script provides detailed output including:
- Pre-migration analysis
- Data migration results
- Validation checks
- Final verification

### 3. Verify Results
After migration, verify the results:
```sql
-- Check all drafts have learning_node_id
SELECT COUNT(*) as total_drafts, 
       COUNT(learning_node_id) as with_learning_node 
FROM draft_exercise;

-- Verify foreign key relationships
SELECT de.public_id, ln.title 
FROM draft_exercise de 
JOIN learning_node ln ON de.learning_node_id = ln.id 
LIMIT 5;
```

## What the Migration Does

1. **Adds** `learning_node_id` column to `draft_exercise` table
2. **Migrates** data from `draft_learning_node_exercise` to the new column
3. **Handles** multiple associations by taking the first one (by created_at)
4. **Creates** backup table `draft_learning_node_exercise_backup`
5. **Adds** foreign key constraint and index
6. **Drops** the old association table
7. **Provides** comprehensive verification and reporting

## Handling Multiple Associations

If any drafts have multiple learning node associations:
- The migration takes the **first association** (ordered by `created_at`)
- All original associations are preserved in the backup table
- The migration script reports which drafts had multiple associations

## Rollback Procedure

If you need to rollback the migration:

```bash
psql -U your_username -d your_database -f migrations/005_simplify_draft_exercise_relationships_rollback.sql
```

The rollback script will:
1. Recreate the association table
2. Restore all associations from the backup
3. Remove the direct foreign key column

## Post-Migration Steps

1. **Deploy updated application code** that uses the new relationship structure
2. **Test the application** thoroughly
3. **Monitor** for any issues
4. **Clean up backup table** after confirming everything works:
   ```sql
   DROP TABLE draft_learning_node_exercise_backup;
   ```

## Troubleshooting

### Migration Fails
- Check the error message in the migration output
- Verify all referenced tables exist
- Ensure no foreign key violations

### Data Inconsistencies
- Review the backup table: `draft_learning_node_exercise_backup`
- Check for orphaned records
- Verify learning node references are valid

### Application Errors
- Ensure application code is updated to use `learning_node_id` instead of associations
- Check that all imports are updated
- Verify query changes are deployed

## Safety Features

- **Transaction-wrapped**: Entire migration runs in a single transaction
- **Idempotent**: Safe to run multiple times
- **Backup creation**: Original data preserved
- **Validation checks**: Comprehensive verification at each step
- **Detailed reporting**: Full visibility into migration process

## Contact
If you encounter issues, the migration output provides detailed information about what happened at each step.
