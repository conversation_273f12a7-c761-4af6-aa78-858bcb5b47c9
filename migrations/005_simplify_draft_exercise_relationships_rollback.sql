-- ROLLBACK Migration: Restore Draft Exercise Learning Node Many-to-Many Relationships
-- Description: Rollback script to restore the association table structure
-- This script reverses the changes made by 005_simplify_draft_exercise_relationships.sql
-- Date: 2025-01-10
-- Author: System Migration Rollback

-- Start transaction for atomicity
BEGIN;

-- ============================================================================
-- STEP 1: PRE-ROLLBACK VERIFICATION
-- ============================================================================

SELECT 'PRE-ROLLBACK VERIFICATION' as step;

-- Verify backup table exists
SELECT 
    'Backup Table Check' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'draft_learning_node_exercise_backup')
        THEN 'EXISTS - Rollback can proceed'
        ELSE 'MISSING - Cannot rollback without backup data'
    END as status;

-- Count current draft exercises with direct learning_node_id
SELECT 
    'Current Draft Exercises' as table_name,
    COUNT(*) as total_count,
    COUNT(learning_node_id) as with_learning_node_id
FROM draft_exercise;

-- ============================================================================
-- STEP 2: RECREATE ASSOCIATION TABLE
-- ============================================================================

SELECT 'RECREATING ASSOCIATION TABLE' as step;

-- Recreate the draft_learning_node_exercise table
CREATE TABLE IF NOT EXISTS draft_learning_node_exercise (
    id SERIAL PRIMARY KEY,
    learning_node_id INTEGER NOT NULL REFERENCES learning_node(id) ON DELETE CASCADE,
    draft_exercise_id INTEGER NOT NULL REFERENCES draft_exercise(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(learning_node_id, draft_exercise_id)
);

-- Recreate indexes
CREATE INDEX IF NOT EXISTS idx_draft_ln_exercise_node ON draft_learning_node_exercise(learning_node_id);
CREATE INDEX IF NOT EXISTS idx_draft_ln_exercise_draft ON draft_learning_node_exercise(draft_exercise_id);

-- ============================================================================
-- STEP 3: RESTORE ASSOCIATION DATA
-- ============================================================================

SELECT 'RESTORING ASSOCIATION DATA' as step;

-- Method 1: Restore from backup table (if it exists)
INSERT INTO draft_learning_node_exercise (learning_node_id, draft_exercise_id, created_at)
SELECT 
    dlneb.learning_node_id,
    dlneb.draft_exercise_id,
    dlneb.created_at
FROM draft_learning_node_exercise_backup dlneb
WHERE EXISTS (
    SELECT 1 FROM draft_exercise de WHERE de.id = dlneb.draft_exercise_id
)
AND EXISTS (
    SELECT 1 FROM learning_node ln WHERE ln.id = dlneb.learning_node_id
)
ON CONFLICT (learning_node_id, draft_exercise_id) DO NOTHING;

-- Method 2: If backup doesn't exist, create associations from current learning_node_id
INSERT INTO draft_learning_node_exercise (learning_node_id, draft_exercise_id, created_at)
SELECT 
    de.learning_node_id,
    de.id,
    de.created_at
FROM draft_exercise de
WHERE de.learning_node_id IS NOT NULL
AND NOT EXISTS (
    SELECT 1 FROM draft_learning_node_exercise dlne 
    WHERE dlne.draft_exercise_id = de.id AND dlne.learning_node_id = de.learning_node_id
)
ON CONFLICT (learning_node_id, draft_exercise_id) DO NOTHING;

-- Report restoration results
SELECT 
    'Association Restoration Results' as step,
    COUNT(*) as total_associations_restored
FROM draft_learning_node_exercise;

-- ============================================================================
-- STEP 4: REMOVE DIRECT FOREIGN KEY COLUMN
-- ============================================================================

SELECT 'REMOVING DIRECT FOREIGN KEY COLUMN' as step;

-- Drop the foreign key constraint first
ALTER TABLE draft_exercise 
DROP CONSTRAINT IF EXISTS fk_draft_exercise_learning_node;

-- Drop the index
DROP INDEX IF EXISTS idx_draft_exercise_learning_node;

-- Remove the learning_node_id column
ALTER TABLE draft_exercise 
DROP COLUMN IF EXISTS learning_node_id;

-- ============================================================================
-- STEP 5: FINAL VERIFICATION
-- ============================================================================

SELECT 'FINAL VERIFICATION' as step;

-- Verify rollback was successful
SELECT 
    'Rollback Summary' as summary,
    (SELECT COUNT(*) FROM draft_exercise) as total_drafts,
    (SELECT COUNT(*) FROM draft_learning_node_exercise) as total_associations,
    (SELECT COUNT(DISTINCT draft_exercise_id) FROM draft_learning_node_exercise) as drafts_with_associations
;

-- Show sample of restored data
SELECT 
    'Sample Restored Data' as verification,
    de.public_id as draft_id,
    de.exercise_type,
    de.status,
    ln.title as learning_node_title,
    dlne.created_at as association_created
FROM draft_exercise de
JOIN draft_learning_node_exercise dlne ON de.id = dlne.draft_exercise_id
JOIN learning_node ln ON dlne.learning_node_id = ln.id
ORDER BY de.created_at DESC
LIMIT 10;

-- Check for drafts without associations
SELECT 
    'Drafts Without Associations' as check_type,
    COUNT(*) as count
FROM draft_exercise de
WHERE NOT EXISTS (
    SELECT 1 FROM draft_learning_node_exercise dlne 
    WHERE dlne.draft_exercise_id = de.id
);

SELECT 'ROLLBACK COMPLETED SUCCESSFULLY' as final_status;

-- Commit the transaction
COMMIT;

-- ============================================================================
-- POST-ROLLBACK NOTES
-- ============================================================================

/*
POST-ROLLBACK NOTES:

1. ASSOCIATION TABLE RESTORED: The draft_learning_node_exercise table has been
   recreated with all original associations.

2. BACKUP TABLE: The backup table 'draft_learning_node_exercise_backup' still
   exists and can be used for verification or dropped if no longer needed.

3. APPLICATION CODE: Ensure your application code is reverted to use the
   association table relationship before deploying.

4. DATA INTEGRITY: All original associations have been restored. If any drafts
   are missing associations, they may need manual attention.

5. CLEANUP: After confirming everything works correctly, you can drop the
   backup table:
   DROP TABLE draft_learning_node_exercise_backup;
*/
