import { ExerciseType } from '@/types/internal/editorial';
import { BaseSolutionData } from '@/components/internal/SolutionEditor';

/**
 * Solution template generators for each exercise type
 * These create properly structured solution objects with default values
 */

export const createDefaultSolution = {
  'mc-simple': (exerciseData?: Record<string, any>): BaseSolutionData => {
    const options = exerciseData?.options || [];
    const firstOptionId = options[0]?.public_id || options[0]?.publicId || '';

    return {
      correctAnswer: {
        correctOptionId: firstOptionId ? [firstOptionId] : []
      },
      solutionSteps: [
        { text: 'Analysez chaque option proposée.' },
        { text: 'Identifiez la réponse qui correspond le mieux à la question.' }
      ]
    };
  },

  'mc-multi': (exerciseData?: Record<string, any>): BaseSolutionData => {
    return {
      correctAnswer: {
        correctOptionIds: []
      },
      solutionSteps: [
        { text: 'Examinez chaque option individuellement.' },
        { text: 'Sélectionnez toutes les réponses correctes.' }
      ]
    };
  },

  'input': (exerciseData?: Record<string, any>): BaseSolutionData => {
    return {
      correctAnswer: {
        correctAnswer: ''
      },
      solutionSteps: [
        { text: 'Analysez la question posée.' },
        { text: 'Formulez une réponse précise et complète.' }
      ]
    };
  },

  'cloze': (exerciseData?: Record<string, any>): BaseSolutionData => {
    const textParts = exerciseData?.text_parts || [];
    const blanksCount = Math.max(0, textParts.length - 1);

    return {
      correctAnswer: {
        correctAnswers: new Array(blanksCount).fill('')
      },
      solutionSteps: [
        { text: 'Lisez attentivement le texte complet.' },
        { text: 'Identifiez le contexte de chaque espace à remplir.' },
        { text: 'Choisissez les mots qui complètent logiquement le sens.' }
      ]
    };
  },

  'dropdown': (exerciseData?: Record<string, any>): BaseSolutionData => {
    const options = exerciseData?.options || [];

    return {
      correctAnswer: {
        correctSelections: new Array(options.length).fill('')
      },
      solutionSteps: [
        { text: 'Analysez le contexte de chaque menu déroulant.' },
        { text: 'Sélectionnez l\'option la plus appropriée pour chaque position.' }
      ]
    };
  },

  'highlight': (exerciseData?: Record<string, any>): BaseSolutionData => {
    return {
      correctAnswer: {
        correctIndices: []
      },
      solutionSteps: [
        { text: 'Lisez attentivement tout le texte.' },
        { text: 'Identifiez les parties qui répondent à la question.' },
        { text: 'Surlignez uniquement les éléments pertinents.' }
      ]
    };
  },

  'matching-pairs': (exerciseData?: Record<string, any>): BaseSolutionData => {
    return {
      correctAnswer: {
        correctPairs: {}
      },
      solutionSteps: [
        { text: 'Examinez tous les éléments des deux colonnes.' },
        { text: 'Identifiez les relations logiques entre les éléments.' },
        { text: 'Associez chaque élément à son partenaire correct.' }
      ]
    };
  },

  'categorize': (exerciseData?: Record<string, any>): BaseSolutionData => {
    return {
      correctAnswer: {
        correctCategories: {}
      },
      solutionSteps: [
        { text: 'Analysez les caractéristiques de chaque élément.' },
        { text: 'Identifiez les critères de classification.' },
        { text: 'Placez chaque élément dans la catégorie appropriée.' }
      ]
    };
  },

  'error-correction': (exerciseData?: Record<string, any>): BaseSolutionData => {
    return {
      correctAnswer: {
        corrections: []
      },
      solutionSteps: [
        { text: 'Lisez attentivement chaque partie du texte.' },
        { text: 'Identifiez les erreurs grammaticales, orthographiques ou de sens.' },
        { text: 'Proposez la correction appropriée pour chaque erreur.' }
      ]
    };
  },

  'true-false': (exerciseData?: Record<string, any>): BaseSolutionData => {
    return {
      correctAnswer: {
        isTrue: true
      },
      solutionSteps: [
        { text: 'Analysez l\'énoncé proposé.' },
        { text: 'Vérifiez la véracité de l\'affirmation.' },
        { text: 'Justifiez votre réponse avec des arguments précis.' }
      ]
    };
  }
};

/**
 * Get a default solution template for a specific exercise type
 */
export function getDefaultSolution(exerciseType: ExerciseType, exerciseData?: Record<string, any>): BaseSolutionData {
  const generator = createDefaultSolution[exerciseType];
  if (!generator) {
    throw new Error(`No solution template available for exercise type: ${exerciseType}`);
  }
  return generator(exerciseData);
}

/**
 * Validate that a solution has the required structure for its exercise type
 */
export function validateSolutionStructure(exerciseType: ExerciseType, solutionData: BaseSolutionData): string[] {
  const errors: string[] = [];

  if (!solutionData.correctAnswer) {
    errors.push('Solution must have a correctAnswer field');
    return errors;
  }

  const correctAnswer = solutionData.correctAnswer;

  switch (exerciseType) {
    case 'mc-simple':
      if (!correctAnswer.correctOptionId || !Array.isArray(correctAnswer.correctOptionId)) {
        errors.push('MC Simple solution must have correctOptionId as an array');
      } else if (correctAnswer.correctOptionId.length !== 1) {
        errors.push('MC Simple solution must have exactly one correct option');
      }
      break;

    case 'mc-multi':
      if (!correctAnswer.correctOptionIds || !Array.isArray(correctAnswer.correctOptionIds)) {
        errors.push('MC Multi solution must have correctOptionIds as an array');
      }
      break;

    case 'input':
      if (typeof correctAnswer.correctAnswer !== 'string') {
        errors.push('Input solution must have correctAnswer as a string');
      }
      break;

    case 'cloze':
      if (!correctAnswer.correctAnswers || !Array.isArray(correctAnswer.correctAnswers)) {
        errors.push('Cloze solution must have correctAnswers as an array');
      }
      break;

    case 'dropdown':
      if (!correctAnswer.correctSelections || !Array.isArray(correctAnswer.correctSelections)) {
        errors.push('Dropdown solution must have correctSelections as an array');
      }
      break;

    case 'highlight':
      if (!correctAnswer.correctIndices || !Array.isArray(correctAnswer.correctIndices)) {
        errors.push('Highlight solution must have correctIndices as an array');
      }
      break;

    case 'matching-pairs':
      if (!correctAnswer.correctPairs || typeof correctAnswer.correctPairs !== 'object') {
        errors.push('Matching Pairs solution must have correctPairs as an object');
      }
      break;

    case 'categorize':
      if (!correctAnswer.correctCategories || typeof correctAnswer.correctCategories !== 'object') {
        errors.push('Categorize solution must have correctCategories as an object');
      }
      break;

    case 'error-correction':
      if (!correctAnswer.corrections || !Array.isArray(correctAnswer.corrections)) {
        errors.push('Error Correction solution must have corrections as an array');
      }
      break;

    case 'true-false':
      if (typeof correctAnswer.isTrue !== 'boolean') {
        errors.push('True/False solution must have isTrue as a boolean');
      }
      break;

    default:
      errors.push(`Unknown exercise type: ${exerciseType}`);
  }

  return errors;
}

/**
 * Check if a solution is complete (has all required fields filled)
 */
export function isSolutionComplete(exerciseType: ExerciseType, solutionData: BaseSolutionData): boolean {
  const errors = validateSolutionStructure(exerciseType, solutionData);
  if (errors.length > 0) {
    return false;
  }

  const correctAnswer = solutionData.correctAnswer;

  switch (exerciseType) {
    case 'mc-simple':
      return correctAnswer.correctOptionId.length > 0 && correctAnswer.correctOptionId[0].trim() !== '';

    case 'mc-multi':
      return correctAnswer.correctOptionIds.length > 0;

    case 'input':
      return correctAnswer.correctAnswer.trim() !== '';

    case 'cloze':
      return correctAnswer.correctAnswers.some((answer: string) => answer.trim() !== '');

    case 'dropdown':
      return correctAnswer.correctSelections.some((selection: string) => selection.trim() !== '');

    case 'highlight':
      return correctAnswer.correctIndices.length > 0;

    case 'matching-pairs':
      return Object.keys(correctAnswer.correctPairs).length > 0;

    case 'categorize':
      return Object.keys(correctAnswer.correctCategories).length > 0;

    case 'error-correction':
      return correctAnswer.corrections.length > 0 &&
             correctAnswer.corrections.some((correction: any) => correction.text?.trim());

    case 'true-false':
      return typeof correctAnswer.isTrue === 'boolean';

    default:
      return false;
  }
}
