import { ExerciseType } from '@/types/internal/editorial';
import { BaseSolutionData } from '@/components/internal/SolutionEditor';

/**
 * Solution template generators for each exercise type
 * These create properly structured solution objects with default values
 */

export const createDefaultSolution = {
  'mc-simple': (exerciseData?: Record<string, any>): BaseSolutionData => {
    const options = exerciseData?.options || [];
    const firstOptionId = options[0]?.public_id || options[0]?.publicId || '';

    return {
      correctAnswer: {
        correct_option_id: firstOptionId ? [firstOptionId] : []
      },
      solutionSteps: [
        { text: 'Analysez chaque option proposée.' },
        { text: 'Identifiez la réponse qui correspond le mieux à la question.' }
      ]
    };
  },

  'mc-multi': (exerciseData?: Record<string, any>): BaseSolutionData => {
    return {
      correctAnswer: {
        correct_option_ids: []
      },
      solutionSteps: [
        { text: 'Examinez chaque option individuellement.' },
        { text: 'Sélectionnez toutes les réponses correctes.' }
      ]
    };
  },

  'input': (exerciseData?: Record<string, any>): BaseSolutionData => {
    return {
      correctAnswer: {
        correct_answer: ''
      },
      solutionSteps: [
        { text: 'Analysez la question posée.' },
        { text: 'Formulez une réponse précise et complète.' }
      ]
    };
  },

  'cloze': (exerciseData?: Record<string, any>): BaseSolutionData => {
    const textParts = exerciseData?.text_parts || [];
    const blanksCount = Math.max(0, textParts.length - 1);

    return {
      correctAnswer: {
        correct_answers: new Array(blanksCount).fill('')
      },
      solutionSteps: [
        { text: 'Lisez attentivement le texte complet.' },
        { text: 'Identifiez le contexte de chaque espace à remplir.' },
        { text: 'Choisissez les mots qui complètent logiquement le sens.' }
      ]
    };
  },

  'dropdown': (exerciseData?: Record<string, any>): BaseSolutionData => {
    const options = exerciseData?.options || [];

    return {
      correctAnswer: {
        correct_selections: new Array(options.length).fill('')
      },
      solutionSteps: [
        { text: 'Analysez le contexte de chaque menu déroulant.' },
        { text: 'Sélectionnez l\'option la plus appropriée pour chaque position.' }
      ]
    };
  },

  'highlight': (exerciseData?: Record<string, any>): BaseSolutionData => {
    return {
      correctAnswer: {
        correct_indices: []
      },
      solutionSteps: [
        { text: 'Lisez attentivement tout le texte.' },
        { text: 'Identifiez les parties qui répondent à la question.' },
        { text: 'Surlignez uniquement les éléments pertinents.' }
      ]
    };
  },

  'matching-pairs': (exerciseData?: Record<string, any>): BaseSolutionData => {
    return {
      correctAnswer: {
        correct_pairs: {}
      },
      solutionSteps: [
        { text: 'Examinez tous les éléments des deux colonnes.' },
        { text: 'Identifiez les relations logiques entre les éléments.' },
        { text: 'Associez chaque élément à son partenaire correct.' }
      ]
    };
  },

  'categorize': (exerciseData?: Record<string, any>): BaseSolutionData => {
    return {
      correctAnswer: {
        correct_categories: {}
      },
      solutionSteps: [
        { text: 'Analysez les caractéristiques de chaque élément.' },
        { text: 'Identifiez les critères de classification.' },
        { text: 'Placez chaque élément dans la catégorie appropriée.' }
      ]
    };
  },

  'error-correction': (exerciseData?: Record<string, any>): BaseSolutionData => {
    return {
      correctAnswer: {
        corrections: []
      },
      solutionSteps: [
        { text: 'Lisez attentivement chaque partie du texte.' },
        { text: 'Identifiez les erreurs grammaticales, orthographiques ou de sens.' },
        { text: 'Proposez la correction appropriée pour chaque erreur.' }
      ]
    };
  },

  'true-false': (exerciseData?: Record<string, any>): BaseSolutionData => {
    return {
      correctAnswer: {
        is_true: true
      },
      solutionSteps: [
        { text: 'Analysez l\'énoncé proposé.' },
        { text: 'Vérifiez la véracité de l\'affirmation.' },
        { text: 'Justifiez votre réponse avec des arguments précis.' }
      ]
    };
  }
};

/**
 * Get a default solution template for a specific exercise type
 */
export function getDefaultSolution(exerciseType: ExerciseType, exerciseData?: Record<string, any>): BaseSolutionData {
  const generator = createDefaultSolution[exerciseType];
  if (!generator) {
    throw new Error(`No solution template available for exercise type: ${exerciseType}`);
  }
  return generator(exerciseData);
}

/**
 * Validate that a solution has the required structure for its exercise type
 */
export function validateSolutionStructure(exerciseType: ExerciseType, solutionData: BaseSolutionData): string[] {
  const errors: string[] = [];

  if (!solutionData.correctAnswer) {
    errors.push('Solution must have a correctAnswer field');
    return errors;
  }

  const correctAnswer = solutionData.correctAnswer;

  switch (exerciseType) {
    case 'mc-simple':
      if (!correctAnswer.correct_option_id || !Array.isArray(correctAnswer.correct_option_id)) {
        errors.push('MC Simple solution must have correct_option_id as an array');
      } else if (correctAnswer.correct_option_id.length !== 1) {
        errors.push('MC Simple solution must have exactly one correct option');
      }
      break;

    case 'mc-multi':
      if (!correctAnswer.correct_option_ids || !Array.isArray(correctAnswer.correct_option_ids)) {
        errors.push('MC Multi solution must have correct_option_ids as an array');
      }
      break;

    case 'input':
      if (typeof correctAnswer.correct_answer !== 'string') {
        errors.push('Input solution must have correct_answer as a string');
      }
      break;

    case 'cloze':
      if (!correctAnswer.correct_answers || !Array.isArray(correctAnswer.correct_answers)) {
        errors.push('Cloze solution must have correct_answers as an array');
      }
      break;

    case 'dropdown':
      if (!correctAnswer.correct_selections || !Array.isArray(correctAnswer.correct_selections)) {
        errors.push('Dropdown solution must have correct_selections as an array');
      }
      break;

    case 'highlight':
      if (!correctAnswer.correct_indices || !Array.isArray(correctAnswer.correct_indices)) {
        errors.push('Highlight solution must have correct_indices as an array');
      }
      break;

    case 'matching-pairs':
      if (!correctAnswer.correct_pairs || typeof correctAnswer.correct_pairs !== 'object') {
        errors.push('Matching Pairs solution must have correct_pairs as an object');
      }
      break;

    case 'categorize':
      if (!correctAnswer.correct_categories || typeof correctAnswer.correct_categories !== 'object') {
        errors.push('Categorize solution must have correct_categories as an object');
      }
      break;

    case 'error-correction':
      if (!correctAnswer.corrections || !Array.isArray(correctAnswer.corrections)) {
        errors.push('Error Correction solution must have corrections as an array');
      }
      break;

    case 'true-false':
      if (typeof correctAnswer.is_true !== 'boolean') {
        errors.push('True/False solution must have is_true as a boolean');
      }
      break;

    default:
      errors.push(`Unknown exercise type: ${exerciseType}`);
  }

  return errors;
}

/**
 * Check if a solution is complete (has all required fields filled)
 */
export function isSolutionComplete(exerciseType: ExerciseType, solutionData: BaseSolutionData): boolean {
  const errors = validateSolutionStructure(exerciseType, solutionData);
  if (errors.length > 0) {
    return false;
  }

  const correctAnswer = solutionData.correctAnswer;

  switch (exerciseType) {
    case 'mc-simple':
      return correctAnswer.correct_option_id.length > 0 && correctAnswer.correct_option_id[0].trim() !== '';

    case 'mc-multi':
      return correctAnswer.correct_option_ids.length > 0;

    case 'input':
      return correctAnswer.correct_answer.trim() !== '';

    case 'cloze':
      return correctAnswer.correct_answers.some((answer: string) => answer.trim() !== '');

    case 'dropdown':
      return correctAnswer.correct_selections.some((selection: string) => selection.trim() !== '');

    case 'highlight':
      return correctAnswer.correct_indices.length > 0;

    case 'matching-pairs':
      return Object.keys(correctAnswer.correct_pairs).length > 0;

    case 'categorize':
      return Object.keys(correctAnswer.correct_categories).length > 0;

    case 'error-correction':
      return correctAnswer.corrections.length > 0 && 
             correctAnswer.corrections.some((correction: any) => correction.text?.trim());

    case 'true-false':
      return typeof correctAnswer.is_true === 'boolean';

    default:
      return false;
  }
}
