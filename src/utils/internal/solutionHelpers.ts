import { ExerciseType } from '@/types/internal/editorial';
import { BaseSolutionData } from '@/components/internal/SolutionEditor';
import { getDefaultSolution, validateSolutionStructure, isSolutionComplete } from './solutionTemplates';

/**
 * Helper functions for working with exercise solutions in the draft editor
 */

/**
 * Initialize solution data for a new exercise
 */
export function initializeSolution(exerciseType: ExerciseType, exerciseData?: Record<string, any>): BaseSolutionData {
  return getDefaultSolution(exerciseType, exerciseData);
}

/**
 * Migrate solution data when exercise type changes
 */
export function migrateSolution(
  fromType: ExerciseType, 
  toType: ExerciseType, 
  currentSolution: BaseSolutionData,
  exerciseData?: Record<string, any>
): BaseSolutionData {
  // If types are the same, return current solution
  if (fromType === toType) {
    return currentSolution;
  }

  // Start with a fresh template for the new type
  const newSolution = getDefaultSolution(toType, exerciseData);

  // Try to preserve solution steps and video if they exist
  if (currentSolution.solutionSteps) {
    newSolution.solutionSteps = currentSolution.solutionSteps;
  }

  if (currentSolution.videoPublicId) {
    newSolution.videoPublicId = currentSolution.videoPublicId;
  }

  // Try to migrate some answer data for compatible types
  try {
    const currentAnswer = currentSolution.correctAnswer;
    
    // MC Simple to MC Multi (and vice versa)
    if ((fromType === 'mc-simple' && toType === 'mc-multi') ||
        (fromType === 'mc-multi' && toType === 'mc-simple')) {
      if (fromType === 'mc-simple' && currentAnswer?.correctOptionId) {
        newSolution.correctAnswer.correctOptionIds = currentAnswer.correctOptionId;
      } else if (fromType === 'mc-multi' && currentAnswer?.correctOptionIds) {
        newSolution.correctAnswer.correctOptionId = currentAnswer.correctOptionIds.slice(0, 1);
      }
    }

    // Input to True/False (if answer is boolean-like)
    if (fromType === 'input' && toType === 'true-false' && currentAnswer?.correctAnswer) {
      const answer = currentAnswer.correctAnswer.toLowerCase().trim();
      if (answer === 'true' || answer === 'vrai' || answer === 'yes' || answer === 'oui') {
        newSolution.correctAnswer.isTrue = true;
      } else if (answer === 'false' || answer === 'faux' || answer === 'no' || answer === 'non') {
        newSolution.correctAnswer.isTrue = false;
      }
    }

    // True/False to Input
    if (fromType === 'true-false' && toType === 'input' && typeof currentAnswer?.isTrue === 'boolean') {
      newSolution.correctAnswer.correctAnswer = currentAnswer.isTrue ? 'Vrai' : 'Faux';
    }
  } catch (error) {
    // If migration fails, just use the default template
    console.warn('Failed to migrate solution data:', error);
  }

  return newSolution;
}

/**
 * Validate solution data and return user-friendly error messages
 */
export function validateSolution(exerciseType: ExerciseType, solutionData: BaseSolutionData): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors = validateSolutionStructure(exerciseType, solutionData);
  const warnings: string[] = [];

  // Check for completeness
  if (!isSolutionComplete(exerciseType, solutionData)) {
    warnings.push('Solution is not complete. Please fill in all required fields.');
  }

  // Check for solution steps
  if (!solutionData.solutionSteps || solutionData.solutionSteps.length === 0) {
    warnings.push('Consider adding solution steps to help students understand the answer.');
  } else {
    const emptySteps = solutionData.solutionSteps.filter(step =>
      !step.text?.trim() && !step.math?.trim() && !step.imagePublicId?.trim()
    );
    if (emptySteps.length > 0) {
      warnings.push(`${emptySteps.length} solution step(s) are empty.`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Get a human-readable description of what the solution contains
 */
export function getSolutionSummary(exerciseType: ExerciseType, solutionData: BaseSolutionData): string {
  if (!solutionData.correctAnswer) {
    return 'No solution configured';
  }

  const correctAnswer = solutionData.correctAnswer;
  const parts: string[] = [];

  switch (exerciseType) {
    case 'mc-simple':
      if (correctAnswer.correctOptionId?.length > 0) {
        parts.push('1 correct option selected');
      }
      break;

    case 'mc-multi':
      const optionCount = correctAnswer.correctOptionIds?.length || 0;
      if (optionCount > 0) {
        parts.push(`${optionCount} correct option(s) selected`);
      }
      break;

    case 'input':
      if (correctAnswer.correctAnswer?.trim()) {
        parts.push(`Answer: "${correctAnswer.correctAnswer}"`);
      }
      break;

    case 'cloze':
      const filledBlanks = correctAnswer.correctAnswers?.filter((a: string) => a.trim()).length || 0;
      const totalBlanks = correctAnswer.correctAnswers?.length || 0;
      if (filledBlanks > 0) {
        parts.push(`${filledBlanks}/${totalBlanks} blanks filled`);
      }
      break;

    case 'dropdown':
      const filledDropdowns = correctAnswer.correctSelections?.filter((s: string) => s.trim()).length || 0;
      const totalDropdowns = correctAnswer.correctSelections?.length || 0;
      if (filledDropdowns > 0) {
        parts.push(`${filledDropdowns}/${totalDropdowns} dropdowns configured`);
      }
      break;

    case 'highlight':
      const highlightCount = correctAnswer.correctIndices?.length || 0;
      if (highlightCount > 0) {
        parts.push(`${highlightCount} part(s) to highlight`);
      }
      break;

    case 'matching-pairs':
      const pairCount = Object.keys(correctAnswer.correctPairs || {}).length;
      if (pairCount > 0) {
        parts.push(`${pairCount} pair(s) matched`);
      }
      break;

    case 'categorize':
      const categorizedCount = Object.keys(correctAnswer.correctCategories || {}).length;
      if (categorizedCount > 0) {
        parts.push(`${categorizedCount} item(s) categorized`);
      }
      break;

    case 'error-correction':
      const correctionCount = correctAnswer.corrections?.length || 0;
      if (correctionCount > 0) {
        parts.push(`${correctionCount} correction(s) defined`);
      }
      break;

    case 'true-false':
      if (typeof correctAnswer.isTrue === 'boolean') {
        parts.push(`Answer: ${correctAnswer.isTrue ? 'True' : 'False'}`);
      }
      break;
  }

  // Add solution steps info
  const stepCount = solutionData.solutionSteps?.length || 0;
  if (stepCount > 0) {
    parts.push(`${stepCount} solution step(s)`);
  }

  // Add video info
  if (solutionData.videoPublicId) {
    parts.push('Video explanation');
  }

  return parts.length > 0 ? parts.join(', ') : 'No solution configured';
}

/**
 * Check if solution data needs to be updated when exercise data changes
 */
export function shouldUpdateSolution(
  exerciseType: ExerciseType,
  oldExerciseData: Record<string, any>,
  newExerciseData: Record<string, any>,
  currentSolution: BaseSolutionData
): boolean {
  switch (exerciseType) {
    case 'mc-simple':
    case 'mc-multi':
      // Check if options changed
      const oldOptions = oldExerciseData.options || [];
      const newOptions = newExerciseData.options || [];
      return JSON.stringify(oldOptions) !== JSON.stringify(newOptions);

    case 'cloze':
      // Check if text parts changed (affects number of blanks)
      const oldParts = oldExerciseData.text_parts || [];
      const newParts = newExerciseData.text_parts || [];
      return oldParts.length !== newParts.length;

    case 'dropdown':
      // Check if options changed
      const oldDropdownOptions = oldExerciseData.options || [];
      const newDropdownOptions = newExerciseData.options || [];
      return JSON.stringify(oldDropdownOptions) !== JSON.stringify(newDropdownOptions);

    case 'highlight':
      // Check if parts changed
      const oldHighlightParts = oldExerciseData.parts || [];
      const newHighlightParts = newExerciseData.parts || [];
      return JSON.stringify(oldHighlightParts) !== JSON.stringify(newHighlightParts);

    case 'matching-pairs':
      // Check if columns changed
      const oldColumnA = oldExerciseData.columnA || oldExerciseData.column_a || [];
      const newColumnA = newExerciseData.columnA || newExerciseData.column_a || [];
      const oldColumnB = oldExerciseData.columnB || oldExerciseData.column_b || [];
      const newColumnB = newExerciseData.columnB || newExerciseData.column_b || [];
      return JSON.stringify(oldColumnA) !== JSON.stringify(newColumnA) ||
             JSON.stringify(oldColumnB) !== JSON.stringify(newColumnB);

    case 'categorize':
      // Check if categories or options changed
      const oldCategories = oldExerciseData.categories || [];
      const newCategories = newExerciseData.categories || [];
      const oldCategorizeOptions = oldExerciseData.options || [];
      const newCategorizeOptions = newExerciseData.options || [];
      return JSON.stringify(oldCategories) !== JSON.stringify(newCategories) ||
             JSON.stringify(oldCategorizeOptions) !== JSON.stringify(newCategorizeOptions);

    case 'error-correction':
      // Check if parts changed
      const oldErrorParts = oldExerciseData.parts || [];
      const newErrorParts = newExerciseData.parts || [];
      return JSON.stringify(oldErrorParts) !== JSON.stringify(newErrorParts);

    default:
      return false;
  }
}
