"use client"
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronDown } from "lucide-react";
import VerbConjugationTable from "./VerbConjugationTable"
import VerbFormLegend from "./VerbFormLegend"
import { VerbDefinition as Verb, VerbMood } from "@/types"

export default function DesktopVerbView({ verb, moods }: { verb: Verb, moods: VerbMood[] }) {
  // Group conjugations by mood
  const groupedConjugations = moods.map(mood => {
    const moodTenses = mood.tenses.map(tense => {
      return verb.conjugations.find(conj => conj.tenseId === tense.tenseId);
    }).filter((tense): tense is Verb['conjugations'][number] => tense !== undefined);
    
    return {
      mood,
      tenses: moodTenses
    };
  }).filter(group => group.tenses.length > 0);

  // State for active mood
  const [activeMood, setActiveMood] = useState<string>(groupedConjugations[0]?.mood.moodId ?? "");

  // Get the currently active mood's conjugations
  const activeMoodData = groupedConjugations.find(group => group.mood.moodId === activeMood);

  return (
    <div className="space-y-4">
      {/* Folder Tabs */}
      <div className="flex flex-wrap gap-1 mb-4 relative">
        <div className="absolute bottom-0 left-0 right-0 h-px bg-slate-200" />
        {groupedConjugations.map((group, index) => (
          <button
            key={group.mood.moodId}
            onClick={() => setActiveMood(group.mood.moodId)}
            className={`
              relative px-4 py-1.5 text-sm font-medium rounded-t-lg
              transition-all duration-200 
              ${activeMood === group.mood.moodId
                ? 'bg-white text-[#09b880] border-t-2 border-l border-r border-slate-200 border-t-[#09b880] -mb-px z-10'
                : 'bg-slate-50 text-slate-600 hover:bg-slate-100'
              }
            `}
          >
            {group.mood.moodName}
          </button>
        ))}
      </div>

      {/* Content Area */}
      <AnimatePresence mode="wait">
        {activeMoodData && (
          <motion.div
            key={activeMoodData.mood.moodId}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
            className="bg-white rounded-lg border border-slate-200 p-4"
          >
            <div className="mb-6 flex justify-end">
              <VerbFormLegend />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-y-12 gap-x-8 justify-items-start">
              {activeMoodData.tenses.map((tense, tenseIndex) => (
                <motion.div
                  key={tense.tenseId}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    delay: tenseIndex * 0.1,
                    duration: 0.3
                  }}
                  className="max-w-[280px] w-full bg-white rounded-xl overflow-hidden border border-slate-200/60 shadow-sm hover:shadow-md transition-shadow duration-300"
                >
                  <div className="px-4 py-2.5 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-slate-200">
                    <h3 className="font-medium text-slate-700">
                      {tense.tenseName}
                    </h3>
                  </div>
                  
                  <div className="flex justify-center w-full">
                    <VerbConjugationTable
                      verbForms={tense.forms}
                    />
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
