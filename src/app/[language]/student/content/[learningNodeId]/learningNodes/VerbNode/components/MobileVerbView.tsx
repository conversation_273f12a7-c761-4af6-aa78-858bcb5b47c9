"use client";

import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronDown } from "lucide-react";
import VerbConjugationTable from "./VerbConjugationTable";
import VerbFormLegend from "./VerbFormLegend";
import { VerbDefinition as Verb, VerbMood } from "@/types";

export default function MobileVerbView({ verb, moods }: { verb: Verb, moods: VerbMood[] }) {
  const [openTense, setOpenTense] = useState<number | null>(null);
  const [activeMood, setActiveMood] = useState<string>(moods[0]?.moodId ?? "");
  const contentRef = useRef<HTMLDivElement>(null);

  // Group conjugations by mood
  const groupedConjugations = moods.map(mood => {
    const moodTenses = mood.tenses.map(tense => {
      return verb.conjugations.find(conj => conj.tenseId === tense.tenseId);
    }).filter((tense): tense is Verb['conjugations'][number] => tense !== undefined);
    
    return {
      mood,
      tenses: moodTenses
    };
  }).filter(group => group.tenses.length > 0);

  // Get the currently active mood's conjugations
  const activeMoodData = groupedConjugations.find(group => group.mood.moodId === activeMood);

  // Auto-scroll to the opened tense
  useEffect(() => {
    if (openTense !== null && contentRef.current) {
      const element = document.getElementById(`tense-${openTense}`);
      if (element) {
        const yOffset = -20; 
        const y = element.getBoundingClientRect().top + window.pageYOffset + yOffset;
        window.scrollTo({ top: y, behavior: 'smooth' });
      }
    }
  }, [openTense]);

  return (
    <div className="min-h-screen bg-slate-50/30" ref={contentRef}>
      <motion.div 
        className="pb-6"
        initial={{ opacity: 0, x: -10 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        {/* Sticky Header with Mood Selection */}
        <div className="sticky top-0 z-10 bg-white border-b border-slate-200 shadow-sm">
          <div className="px-4 py-3">
            <h3 className="text-lg font-semibold text-slate-700 mb-3">Verb Conjugations</h3>
            <div className="flex gap-2 overflow-x-auto pb-2 scrollbar-hide -mx-4 px-4">
              {groupedConjugations.map((group) => (
                <button
                  key={group.mood.moodId}
                  onClick={() => {
                    setActiveMood(group.mood.moodId);
                    setOpenTense(null);
                  }}
                  className={`
                    px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-all
                    ${activeMood === group.mood.moodId
                      ? 'bg-[#09b880] text-white shadow-sm'
                      : 'bg-slate-100 text-slate-600 hover:bg-slate-200'
                    }
                  `}
                >
                  {group.mood.moodName}
                </button>
              ))}
            </div>
            <div className="mt-3 flex justify-center">
              <VerbFormLegend compact className="text-xs" />
            </div>
          </div>
        </div>
        
        {/* Tenses List */}
        <div className="mt-4 px-4 space-y-3">
          {activeMoodData?.tenses.map((tense, tenseIndex) => (
            <motion.div 
              id={`tense-${tenseIndex}`}
              key={tense.tenseId}
              className="rounded-xl overflow-hidden bg-white border border-slate-200/60 shadow-sm"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ 
                delay: tenseIndex * 0.05,
                duration: 0.2 
              }}
            >
              <button 
                onClick={() => setOpenTense(openTense === tenseIndex ? null : tenseIndex)}
                className={`
                  w-full flex items-center justify-between p-4 text-left transition-all
                  ${openTense === tenseIndex 
                    ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-slate-200' 
                    : 'hover:bg-slate-50'
                  }
                `}
              >
                <span className={`font-medium ${openTense === tenseIndex ? 'text-slate-700' : 'text-slate-600'}`}>
                  {tense.tenseName}
                </span>
                <ChevronDown 
                  className={`h-5 w-5 transition-transform duration-200 ${
                    openTense === tenseIndex ? 'transform rotate-180 text-slate-500' : 'text-slate-400'
                  }`} 
                />
              </button>

              <AnimatePresence>
                {openTense === tenseIndex && (
                  <motion.div 
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.2 }}
                    className="overflow-hidden"
                  >
                    <div className="py-2">
                      <VerbConjugationTable
                        verbForms={tense.forms}
                      />
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};
