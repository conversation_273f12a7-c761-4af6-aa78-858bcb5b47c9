import { cn } from "@/lib/utils";

interface VerbFormLegendProps {
  className?: string;
  compact?: boolean;
}

export default function VerbFormLegend({ className, compact = false }: VerbFormLegendProps) {
  if (compact) {
    return (
      <div className={cn("flex items-center gap-3 text-xs", className)}>
        <div className="flex items-center gap-1.5">
          <span className="text-slate-600">●</span>
          <span className="text-slate-500">Regular</span>
        </div>
        <div className="flex items-center gap-1.5">
          <span className="text-amber-600 font-semibold">●</span>
          <span className="text-slate-500">Irregular</span>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("inline-flex items-center gap-4 px-3 py-2 bg-slate-50 rounded-lg border border-slate-200", className)}>
      <div className="flex items-center gap-2">
        <div className="w-3 h-3 rounded-full bg-slate-600"></div>
        <span className="text-sm text-slate-600">Regular forms</span>
      </div>
      <div className="w-px h-4 bg-slate-300"></div>
      <div className="flex items-center gap-2">
        <div className="w-3 h-3 rounded-full bg-amber-600"></div>
        <span className="text-sm text-slate-600 font-medium">Irregular forms</span>
      </div>
    </div>
  );
}