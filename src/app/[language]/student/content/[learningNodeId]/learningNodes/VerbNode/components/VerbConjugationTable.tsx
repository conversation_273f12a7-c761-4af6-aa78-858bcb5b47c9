import { VerbForm } from "@/types";

interface VerbConjugationTableProps {
  verbForms: VerbForm[];
  showInlineIndicators?: boolean;
}

function VerbConjugationTable({ verbForms, showInlineIndicators = false }: VerbConjugationTableProps) {
  return (
    <div className="w-full">
      {verbForms.map((entry, index) => (
        <div
          key={entry.person}
          className="group grid grid-cols-[minmax(80px,auto)_1fr] items-center py-2 px-4 hover:bg-blue-50/50 transition-colors duration-200 relative"
        >
          <span className="text-blue-600/70 font-medium whitespace-nowrap pr-3">{entry.person}</span>
          <span className={`pl-3 flex items-center gap-2 ${
            entry.irregular 
              ? 'text-amber-600 font-semibold' 
              : 'text-slate-600'
          }`}>
            <span className="whitespace-nowrap overflow-hidden text-ellipsis">{entry.form}</span>
            {showInlineIndicators && entry.irregular && (
              <span className="text-xs bg-amber-100 text-amber-700 px-1.5 py-0.5 rounded font-medium">
                irregular
              </span>
            )}
          </span>
          {index < verbForms.length - 1 && (
            <div className="absolute bottom-0 left-4 right-4 h-px bg-slate-100 group-hover:bg-blue-100/50 transition-colors duration-200" />
          )}
        </div>
      ))}
    </div>
  );
}

export default VerbConjugationTable;