'use client';

import { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertTriangle } from 'lucide-react';

interface DropdownSolutionEditorProps {
  exerciseData: Record<string, any>;
  correctAnswer: any;
  onChange: (correctAnswer: any) => void;
}

export function DropdownSolutionEditor({
  exerciseData,
  correctAnswer,
  onChange
}: DropdownSolutionEditorProps) {
  const [selections, setSelections] = useState<string[]>([]);

  const template = exerciseData.template || '';
  const options = exerciseData.options || [];
  
  // Count dropdowns in template (assuming they're marked with placeholders like {0}, {1}, etc.)
  const dropdownCount = options.length;

  // Initialize selections from correct answer
  useEffect(() => {
    if (correctAnswer?.correctSelections && Array.isArray(correctAnswer.correctSelections)) {
      setSelections(correctAnswer.correctSelections);
    } else {
      // Initialize with empty selections for each dropdown
      setSelections(new Array(dropdownCount).fill(''));
    }
  }, [correctAnswer, dropdownCount]);

  const handleSelectionChange = (index: number, value: string) => {
    const newSelections = [...selections];
    if (value === '__unselected__') {
      newSelections[index] = '';
    } else {
      newSelections[index] = value;
    }
    setSelections(newSelections);
    onChange({
      correctSelections: newSelections
    });
  };

  if (!template || options.length === 0) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          No template or options found in exercise data. Please configure the dropdown exercise first.
        </AlertDescription>
      </Alert>
    );
  }

  const filledSelectionsCount = selections.filter(selection => selection.trim()).length;

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 text-base font-semibold">
        <CheckCircle className="h-4 w-4" />
        Dropdown Selections
      </div>
          <p className="text-sm text-gray-600">
            Select the correct option for each dropdown in the exercise:
          </p>

          {/* Preview of the template */}
          <div className="p-3 bg-gray-50 rounded-lg">
            <p className="text-sm font-medium mb-2">Exercise Template:</p>
            <div className="text-sm font-mono">{template}</div>
          </div>
          
          <div className="space-y-3">
            {options.map((optionSet: string[], index: number) => (
              <div key={index} className="space-y-2">
                <Label className="text-sm font-medium">
                  Dropdown {index + 1}:
                </Label>
                <Select
                  value={selections[index] || '__unselected__'}
                  onValueChange={(value) => handleSelectionChange(index, value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={`Select correct option for dropdown ${index + 1}...`} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="__unselected__">-- No selection --</SelectItem>
                    {optionSet.map((option, optionIndex) => (
                      <SelectItem 
                        key={optionIndex} 
                        value={option || `option-${optionIndex}`}
                      >
                        {option || `Option ${optionIndex + 1}`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {selections[index] && (
                  <div className="text-xs text-gray-600">
                    Selected: <span className="font-medium">{selections[index]}</span>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="flex items-center gap-2">
            <Badge variant={filledSelectionsCount === dropdownCount ? "secondary" : "outline"}>
              {filledSelectionsCount} of {dropdownCount} dropdowns configured
            </Badge>
          </div>

          {filledSelectionsCount > 0 && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                {filledSelectionsCount} dropdown selection(s) configured.
              </AlertDescription>
            </Alert>
          )}

          {filledSelectionsCount === 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Please select the correct option for at least one dropdown.
              </AlertDescription>
            </Alert>
          )}

          <div className="text-xs text-gray-500">
            <p><strong>Note:</strong> Each dropdown must have exactly one correct selection.</p>
          </div>
    </div>
  );
}
