'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  AlertTriangle, 
  Lightbulb, 
  Plus,
  Trash2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { ExerciseType } from '@/types/internal/editorial';
import { validateExerciseSolution } from '@/schemas/internal/exerciseDataValidation';

// Import specific solution editors
import { MCSimpleSolutionEditor } from './MCSimpleSolutionEditor';
import { MCMultiSolutionEditor } from './MCMultiSolutionEditor';
import { InputSolutionEditor } from './InputSolutionEditor';
import { ClozeSolutionEditor } from './ClozeSolutionEditor';
import { DropdownSolutionEditor } from './DropdownSolutionEditor';
import { HighlightSolutionEditor } from './HighlightSolutionEditor';
import { MatchingPairsSolutionEditor } from './MatchingPairsSolutionEditor';
import { CategorizeSolutionEditor } from './CategorizeSolutionEditor';
import { ErrorCorrectionSolutionEditor } from './ErrorCorrectionSolutionEditor';
import { TrueFalseSolutionEditor } from './TrueFalseSolutionEditor';
import { SolutionStepEditor } from './SolutionStepEditor';

export interface SolutionStep {
  text?: string;
  math?: string;
  imagePublicId?: string;
}

export interface BaseSolutionData {
  correctAnswer: any;
  solutionSteps?: SolutionStep[];
  videoPublicId?: string;
}

interface SolutionEditorProps {
  exerciseType: ExerciseType;
  exerciseData: Record<string, any>;
  solutionData: BaseSolutionData;
  onChange: (solutionData: BaseSolutionData) => void;
  className?: string;
}

export default function SolutionEditor({
  exerciseType,
  exerciseData,
  solutionData,
  onChange,
  className
}: SolutionEditorProps) {
  const [validationError, setValidationError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'answer' | 'steps'>('answer');

  // Validate solution data whenever it changes
  useEffect(() => {
    try {
      validateExerciseSolution(solutionData);
      setValidationError(null);
    } catch (error) {
      setValidationError(error instanceof Error ? error.message : 'Invalid solution format');
    }
  }, [solutionData]);

  const handleCorrectAnswerChange = (correctAnswer: any) => {
    onChange({
      ...solutionData,
      correctAnswer: correctAnswer
    });
  };

  const handleSolutionStepsChange = (steps: SolutionStep[]) => {
    onChange({
      ...solutionData,
      solutionSteps: steps
    });
  };


  const addSolutionStep = () => {
    const currentSteps = solutionData.solutionSteps || [];
    handleSolutionStepsChange([
      ...currentSteps,
      { text: '' }
    ]);
  };

  const updateSolutionStep = (index: number, step: SolutionStep) => {
    const currentSteps = solutionData.solutionSteps || [];
    const newSteps = [...currentSteps];
    newSteps[index] = step;
    handleSolutionStepsChange(newSteps);
  };

  const removeSolutionStep = (index: number) => {
    const currentSteps = solutionData.solutionSteps || [];
    const newSteps = currentSteps.filter((_: SolutionStep, i: number) => i !== index);
    handleSolutionStepsChange(newSteps);
  };

  const renderCorrectAnswerEditor = () => {
    const props = {
      exerciseData,
      correctAnswer: solutionData.correctAnswer,
      onChange: handleCorrectAnswerChange
    };

    switch (exerciseType) {
      case 'mc-simple':
        return <MCSimpleSolutionEditor {...props} />;
      case 'mc-multi':
        return <MCMultiSolutionEditor {...props} />;
      case 'input':
        return <InputSolutionEditor {...props} />;
      case 'cloze':
        return <ClozeSolutionEditor {...props} />;
      case 'dropdown':
        return <DropdownSolutionEditor {...props} />;
      case 'highlight':
        return <HighlightSolutionEditor {...props} />;
      case 'matching-pairs':
        return <MatchingPairsSolutionEditor {...props} />;
      case 'categorize':
        return <CategorizeSolutionEditor {...props} />;
      case 'error-correction':
        return <ErrorCorrectionSolutionEditor {...props} />;
      case 'true-false':
        return <TrueFalseSolutionEditor {...props} />;
      default:
        return (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Solution editor not implemented for exercise type: {exerciseType}
            </AlertDescription>
          </Alert>
        );
    }
  };

  return (
    <div className={cn('h-full flex flex-col', className)}>

      {/* Tabs container */}
      <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="h-full flex flex-col">
        <TabsList className="grid w-full grid-cols-2 mb-0">
          <TabsTrigger value="answer" className="flex items-center justify-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Correct Answer
          </TabsTrigger>
          <TabsTrigger value="steps" className="flex items-center justify-center gap-2">
            <Lightbulb className="h-4 w-4" />
            Solution Steps
          </TabsTrigger>
        </TabsList>

        <div className="flex-1 p-4 overflow-auto">
          <TabsContent value="answer" className="mt-0 h-full space-y-0">
            <div className="space-y-4">
              {validationError && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{validationError}</AlertDescription>
                </Alert>
              )}
              {renderCorrectAnswerEditor()}
            </div>
          </TabsContent>

          <TabsContent value="steps" className="mt-0 h-full space-y-0">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Solution Steps</h3>
                <Button onClick={addSolutionStep} size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Step
                </Button>
              </div>
              
              <div className="space-y-3">
                {(solutionData.solutionSteps || []).map((step, index) => (
                  <SolutionStepEditor
                    key={index}
                    step={step}
                    index={index}
                    onChange={(updatedStep) => updateSolutionStep(index, updatedStep)}
                    onRemove={() => removeSolutionStep(index)}
                  />
                ))}

                {(!solutionData.solutionSteps || solutionData.solutionSteps.length === 0) && (
                  <div className="text-center py-8 text-gray-500">
                    <Lightbulb className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                    <p className="text-sm">No solution steps added yet</p>
                    <p className="text-xs mt-1">Click &ldquo;Add Step&rdquo; to provide step-by-step explanations</p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
