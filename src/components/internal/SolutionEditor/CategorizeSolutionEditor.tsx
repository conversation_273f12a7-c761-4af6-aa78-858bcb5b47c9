'use client';

import { useState, useEffect, useMemo, useRef } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertTriangle, FolderOpen } from 'lucide-react';

interface CategorizeSolutionEditorProps {
  exerciseData: Record<string, any>;
  correctAnswer: any;
  onChange: (correctAnswer: any) => void;
}

// Helper to normalize IDs for comparison
const normalizeId = (id: string): string => {
  if (!id) return '';
  // Remove all non-alphanumeric characters for comparison
  return id.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
};

// Helper to create a stable key from an object
const createStableKey = (obj: any): string => {
  return JSON.stringify(obj, Object.keys(obj).sort());
};

export function CategorizeSolutionEditor({
  exerciseData,
  correctAnswer,
  onChange
}: CategorizeSolutionEditorProps) {
  const categories = exerciseData.categories || [];
  const options = exerciseData.options || [];
  
  // Helper to get ID for an item
  const getId = (item: any) => item.publicId || item.public_id || '';
  
  // Track if we've initialized from props
  const hasInitializedRef = useRef(false);
  const lastCorrectAnswerRef = useRef<string>('');
  
  // Local state for categorizations - this is our working copy
  const [localCategorizations, setLocalCategorizations] = useState<Record<string, string>>({});
  
  // Create stable ID mappings
  const idMappings = useMemo(() => {
    const mappings = {
      optionIdMap: new Map<string, string>(), // normalized -> actual
      categoryIdMap: new Map<string, string>(), // normalized -> actual
    };
    
    options.forEach((opt: any) => {
      const id = getId(opt);
      if (id) {
        mappings.optionIdMap.set(normalizeId(id), id);
      }
    });
    
    categories.forEach((cat: any) => {
      const id = getId(cat);
      if (id) {
        mappings.categoryIdMap.set(normalizeId(id), id);
      }
    });
    
    return mappings;
  }, [options, categories]);
  
  // Initialize or update from props only when necessary
  useEffect(() => {
    const currentAnswerKey = createStableKey(correctAnswer?.correctCategories || {});

    // Only update if:
    // 1. We haven't initialized yet, OR
    // 2. The correctAnswer has actually changed (not just a re-render)
    if (!hasInitializedRef.current || currentAnswerKey !== lastCorrectAnswerRef.current) {
      const rawCategorizations = correctAnswer?.correctCategories || {};
      const normalizedCategorizations: Record<string, string> = {};
      
      // Map stored IDs to current IDs
      Object.entries(rawCategorizations).forEach(([storedOptionId, storedCategoryId]) => {
        // Find the actual option ID
        const normalizedOptionId = normalizeId(storedOptionId);
        let actualOptionId = storedOptionId;
        
        // Try to find the current option ID
        for (const [normalized, actual] of idMappings.optionIdMap.entries()) {
          if (normalized === normalizedOptionId) {
            actualOptionId = actual;
            break;
          }
        }
        
        // Find the actual category ID
        const normalizedCategoryId = normalizeId(storedCategoryId as string);
        let actualCategoryId = storedCategoryId as string;
        
        // Try to find the current category ID
        for (const [normalized, actual] of idMappings.categoryIdMap.entries()) {
          if (normalized === normalizedCategoryId) {
            actualCategoryId = actual;
            break;
          }
        }
        
        // Only add if we found valid matches
        const optionExists = options.some((opt: any) => getId(opt) === actualOptionId);
        const categoryExists = categories.some((cat: any) => getId(cat) === actualCategoryId);
        
        if (optionExists && categoryExists) {
          normalizedCategorizations[actualOptionId] = actualCategoryId;
        }
      });
      
      setLocalCategorizations(normalizedCategorizations);
      hasInitializedRef.current = true;
      lastCorrectAnswerRef.current = currentAnswerKey;
      
      if (process.env.NODE_ENV === 'development') {
        console.log('CategorizeSolutionEditor initialized:', {
          raw: rawCategorizations,
          normalized: normalizedCategorizations,
          idMappings: {
            options: Array.from(idMappings.optionIdMap.entries()),
            categories: Array.from(idMappings.categoryIdMap.entries())
          }
        });
      }
    }
  }, [correctAnswer, options, categories, idMappings]);

  const handleCategorizationChange = (optionId: string, categoryId: string) => {
    // Update local state
    const newCategorizations = { ...localCategorizations };
    
    if (categoryId === '__uncategorized__') {
      delete newCategorizations[optionId];
    } else {
      newCategorizations[optionId] = categoryId;
    }
    
    setLocalCategorizations(newCategorizations);
    
    // Update parent with all categorizations (including old format ones)
    // This ensures we don't lose any data that might be in a different format
    const updatedCorrectCategories = { ...correctAnswer?.correctCategories };

    // Remove old entries for this option (in any format)
    const normalizedOptionId = normalizeId(optionId);
    Object.keys(updatedCorrectCategories).forEach(key => {
      if (normalizeId(key) === normalizedOptionId) {
        delete updatedCorrectCategories[key];
      }
    });

    // Add the new categorization
    if (categoryId !== '__uncategorized__') {
      updatedCorrectCategories[optionId] = categoryId;
    }

    onChange({
      ...correctAnswer,
      correctCategories: updatedCorrectCategories
    });
  };

  const categorizedCount = Object.keys(localCategorizations).length;

  // Group options by their assigned categories for preview
  const categoryGroups = useMemo(() => {
    const groups: Record<string, any> = {};
    
    categories?.forEach((category: any) => {
      const categoryId = getId(category);
      if (!categoryId) return;
      
      groups[categoryId] = {
        category,
        options: options?.filter((option: any) => {
          const optionId = getId(option);
          return optionId && localCategorizations[optionId] === categoryId;
        }) || []
      };
    });
    
    return groups;
  }, [categories, options, localCategorizations]);

  if (!categories || !options || categories.length === 0 || options.length === 0) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          No categories or options found in exercise data. Please add categories and options to the exercise first.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 text-base font-semibold">
        <CheckCircle className="h-4 w-4" />
        Categorize Items
      </div>
      <p className="text-sm text-gray-600">
        Assign each option to the correct category:
      </p>
      
      <div className="space-y-3">
        {options.map((option: any, index: number) => {
          const optionId = getId(option);
          if (!optionId) {
            console.warn('Option missing ID:', option);
            return null;
          }
          
          const selectedCategoryId = localCategorizations[optionId] || '__uncategorized__';
          
          return (
            <div key={`${optionId}-${index}`} className="flex items-center gap-3 p-3 border rounded-lg">
              <div className="flex-1">
                <Label className="text-sm font-medium">Item:</Label>
                <p className="text-sm text-gray-700">{option.text || `Option ${index + 1}`}</p>
              </div>
              
              <div className="flex-1">
                <Label className="text-sm font-medium">Category:</Label>
                <Select
                  value={selectedCategoryId}
                  onValueChange={(value) => handleCategorizationChange(optionId, value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="__uncategorized__">-- No category --</SelectItem>
                    {categories.map((category: any, catIndex: number) => {
                      const categoryId = getId(category);
                      if (!categoryId) {
                        console.warn('Category missing ID:', category);
                        return null;
                      }
                      
                      return (
                        <SelectItem key={`${categoryId}-${catIndex}`} value={categoryId}>
                          {category.text || `Category ${catIndex + 1}`}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>
            </div>
          );
        })}
      </div>

      {/* Category preview */}
      {categorizedCount > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium">Category Preview:</h4>
          <div className="grid gap-3">
            {Object.entries(categoryGroups)
              .filter(([_, group]: [string, any]) => group.options.length > 0)
              .map(([categoryId, group]: [string, any]) => (
              <div key={categoryId} className="p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <FolderOpen className="h-4 w-4" />
                  <span className="font-medium text-sm">{group.category.text || 'Unnamed Category'}</span>
                  <Badge variant="outline" className="text-xs">
                    {group.options.length} item(s)
                  </Badge>
                </div>
                {group.options.length > 0 && (
                  <div className="text-xs text-gray-600">
                    {group.options.map((opt: any) => opt.text || 'Unnamed option').join(', ')}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="flex items-center gap-2">
        <Badge variant={categorizedCount === options.length ? "secondary" : "outline"}>
          {categorizedCount} of {options.length} items categorized
        </Badge>
      </div>

      {categorizedCount > 0 && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            {categorizedCount} item(s) have been assigned to categories.
          </AlertDescription>
        </Alert>
      )}

      {categorizedCount === 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Please assign at least one item to a category.
          </AlertDescription>
        </Alert>
      )}

      <div className="text-xs text-gray-500">
        <p><strong>Note:</strong> Each item should be assigned to exactly one category for the correct solution.</p>
      </div>
    </div>
  );
}