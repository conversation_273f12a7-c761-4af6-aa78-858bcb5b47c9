'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Trash2,
  Type,
  Calculator,
  Image,
  GripVertical,
  AlertCircle
} from 'lucide-react';
import { SolutionStep } from './index';
import { LatexRenderer } from '@/components/shared/LatexRenderer';

interface SolutionStepEditorProps {
  step: SolutionStep;
  index: number;
  onChange: (step: SolutionStep) => void;
  onRemove: () => void;
}

export function SolutionStepEditor({
  step,
  index,
  onChange,
  onRemove
}: SolutionStepEditorProps) {
  const [activeTab, setActiveTab] = useState<'text' | 'math' | 'image'>('text');

  const handleTextChange = (text: string) => {
    onChange({
      ...step,
      text: text || undefined
    });
  };

  const handleMathChange = (math: string) => {
    onChange({
      ...step,
      math: math || undefined
    });
  };

  const handleImageChange = (imagePublicId: string) => {
    onChange({
      ...step,
      imagePublicId: imagePublicId || undefined
    });
  };

  const hasContent = !!(step.text?.trim() || step.math?.trim() || step.imagePublicId?.trim());

  return (
    <Card className="relative">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <GripVertical className="h-4 w-4 text-gray-400" />
            <span>Step {index + 1}</span>
            {hasContent && <Badge variant="secondary" className="text-xs">Has content</Badge>}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onRemove}
            className="h-8 w-8 p-0 text-gray-400 hover:text-red-500"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </CardTitle>
      </CardHeader>

      <CardContent className="pt-0">
        <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="text" className="flex items-center gap-1 text-xs">
              <Type className="h-3 w-3" />
              Text
            </TabsTrigger>
            <TabsTrigger value="math" className="flex items-center gap-1 text-xs">
              <Calculator className="h-3 w-3" />
              Math
            </TabsTrigger>
            <TabsTrigger value="image" className="flex items-center gap-1 text-xs">
              {/* eslint-disable-next-line jsx-a11y/alt-text */}
              <Image className="h-3 w-3" />
              Image
            </TabsTrigger>
          </TabsList>

          <div className="mt-3">
            <TabsContent value="text" className="mt-0">
              <div className="space-y-2">
                <Label htmlFor={`step-text-${index}`} className="text-sm">
                  Explanation Text
                </Label>
                <Textarea
                  id={`step-text-${index}`}
                  value={step.text || ''}
                  onChange={(e) => handleTextChange(e.target.value)}
                  placeholder="Enter step explanation..."
                  rows={3}
                  className="resize-none"
                />
                <p className="text-xs text-gray-500">
                  Provide a clear explanation for this solution step.
                </p>
              </div>
            </TabsContent>

            <TabsContent value="math" className="mt-0">
              <div className="space-y-2">
                <Label htmlFor={`step-math-${index}`} className="text-sm">
                  Mathematical Expression (LaTeX)
                </Label>
                <Textarea
                  id={`step-math-${index}`}
                  value={step.math || ''}
                  onChange={(e) => handleMathChange(e.target.value)}
                  placeholder="Enter LaTeX expression... e.g., x = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}"
                  rows={3}
                  className="resize-none font-mono text-sm"
                />
                <p className="text-xs text-gray-500">
                  Use LaTeX syntax for mathematical expressions. They will be rendered with MathJax.
                </p>
                {step.math && (
                  <div className="p-2 bg-gray-50 rounded border">
                    <p className="text-xs text-gray-600 mb-1">Preview:</p>
                    <div className="text-sm">
                      <LatexRenderer content={step.math} fontSize="sm" />
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="image" className="mt-0">
              <div className="space-y-2">
                <Label htmlFor={`step-image-${index}`} className="text-sm opacity-50">
                  Image Public ID
                </Label>
                <Input
                  id={`step-image-${index}`}
                  value=""
                  disabled={true}
                  placeholder="Image upload functionality coming soon..."
                  className="opacity-50"
                />
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Image upload functionality is coming soon. Backend API endpoints are currently being developed.
                  </AlertDescription>
                </Alert>
                <div className="text-center py-4 text-gray-500">
                  {/* eslint-disable-next-line jsx-a11y/alt-text */}
                  <Image className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                  <p className="text-xs">Feature coming soon</p>
                </div>
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  );
}
