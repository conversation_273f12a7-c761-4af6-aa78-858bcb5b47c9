'use client';

import { useState, useEffect, useMemo, useRef } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertTriangle, ArrowRight } from 'lucide-react';

interface MatchingPairsSolutionEditorProps {
  exerciseData: Record<string, any>;
  correctAnswer: any;
  onChange: (correctAnswer: any) => void;
}

// Helper to normalize IDs for comparison
const normalizeId = (id: string): string => {
  if (!id) return '';
  // Remove all non-alphanumeric characters for comparison
  return id.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
};

// Helper to create a stable key from an object
const createStableKey = (obj: any): string => {
  return JSON.stringify(obj, Object.keys(obj).sort());
};

export function MatchingPairsSolutionEditor({
  exerciseData,
  correctAnswer,
  onChange
}: MatchingPairsSolutionEditorProps) {
  const columnA = exerciseData.columnA || exerciseData.column_a || [];
  const columnB = exerciseData.columnB || exerciseData.column_b || [];
  
  // Helper to get ID for an item
  const getId = (item: any) => item.publicId || item.public_id || '';
  
  // Track if we've initialized from props
  const hasInitializedRef = useRef(false);
  const lastCorrectAnswerRef = useRef<string>('');
  
  // Local state for pairs - this is our working copy
  const [localPairs, setLocalPairs] = useState<Record<string, string>>({});
  
  // Create stable ID mappings
  const idMappings = useMemo(() => {
    const mappings = {
      columnAIdMap: new Map<string, string>(), // normalized -> actual
      columnBIdMap: new Map<string, string>(), // normalized -> actual
    };
    
    columnA.forEach((item: any) => {
      const id = getId(item);
      if (id) {
        mappings.columnAIdMap.set(normalizeId(id), id);
      }
    });
    
    columnB.forEach((item: any) => {
      const id = getId(item);
      if (id) {
        mappings.columnBIdMap.set(normalizeId(id), id);
      }
    });
    
    return mappings;
  }, [columnA, columnB]);
  
  // Initialize or update from props only when necessary
  useEffect(() => {
    const currentAnswerKey = createStableKey(correctAnswer?.correctPairs || {});

    // Only update if:
    // 1. We haven't initialized yet, OR
    // 2. The correctAnswer has actually changed (not just a re-render)
    if (!hasInitializedRef.current || currentAnswerKey !== lastCorrectAnswerRef.current) {
      const rawPairs = correctAnswer?.correctPairs || {};
      const normalizedPairs: Record<string, string> = {};
      
      // Map stored IDs to current IDs
      Object.entries(rawPairs).forEach(([storedAId, storedBId]) => {
        // Find the actual column A ID
        const normalizedAId = normalizeId(storedAId);
        let actualAId = storedAId;
        
        // Try to find the current column A ID
        for (const [normalized, actual] of idMappings.columnAIdMap.entries()) {
          if (normalized === normalizedAId) {
            actualAId = actual;
            break;
          }
        }
        
        // Find the actual column B ID
        const normalizedBId = normalizeId(storedBId as string);
        let actualBId = storedBId as string;
        
        // Try to find the current column B ID
        for (const [normalized, actual] of idMappings.columnBIdMap.entries()) {
          if (normalized === normalizedBId) {
            actualBId = actual;
            break;
          }
        }
        
        // Only add if we found valid matches
        const itemAExists = columnA.some((item: any) => getId(item) === actualAId);
        const itemBExists = columnB.some((item: any) => getId(item) === actualBId);
        
        if (itemAExists && itemBExists) {
          normalizedPairs[actualAId] = actualBId;
        }
      });
      
      setLocalPairs(normalizedPairs);
      hasInitializedRef.current = true;
      lastCorrectAnswerRef.current = currentAnswerKey;
      
      if (process.env.NODE_ENV === 'development') {
        console.log('MatchingPairsSolutionEditor initialized:', {
          raw: rawPairs,
          normalized: normalizedPairs,
          idMappings: {
            columnA: Array.from(idMappings.columnAIdMap.entries()),
            columnB: Array.from(idMappings.columnBIdMap.entries())
          }
        });
      }
    }
  }, [correctAnswer, columnA, columnB, idMappings]);

  const handlePairChange = (itemAId: string, itemBId: string) => {
    // Update local state
    const newPairs = { ...localPairs };
    
    if (itemBId === '__no_match__') {
      delete newPairs[itemAId];
    } else {
      newPairs[itemAId] = itemBId;
    }
    
    setLocalPairs(newPairs);
    
    // Update parent with all pairs (including old format ones)
    // This ensures we don't lose any data that might be in a different format
    const updatedCorrectPairs = { ...correctAnswer?.correctPairs };

    // Remove old entries for this item A (in any format)
    const normalizedAId = normalizeId(itemAId);
    Object.keys(updatedCorrectPairs).forEach(key => {
      if (normalizeId(key) === normalizedAId) {
        delete updatedCorrectPairs[key];
      }
    });

    // Add the new pair
    if (itemBId !== '__no_match__') {
      updatedCorrectPairs[itemAId] = itemBId;
    }

    onChange({
      ...correctAnswer,
      correctPairs: updatedCorrectPairs
    });
  };

  if (!columnA || !columnB || columnA.length === 0 || columnB.length === 0) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          No items found in exercise data. Please add items to both columns first.
        </AlertDescription>
      </Alert>
    );
  }

  const pairedCount = Object.keys(localPairs).length;
  const usedColumnBIds = new Set(Object.values(localPairs));

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 text-base font-semibold">
        <CheckCircle className="h-4 w-4" />
        Match Correct Pairs
      </div>
      <p className="text-sm text-gray-600">
        Match each item from Column A with the correct item from Column B:
      </p>
      
      <div className="space-y-3">
        {columnA.map((itemA: any, index: number) => {
          const itemAId = getId(itemA);
          if (!itemAId) {
            console.warn('Column A item missing ID:', itemA);
            return null;
          }
          
          const selectedItemBId = localPairs[itemAId] || '__no_match__';
          
          return (
            <div key={`${itemAId}-${index}`} className="flex items-center gap-3 p-3 border rounded-lg">
              <div className="flex-1">
                <Label className="text-sm font-medium">Column A:</Label>
                <p className="text-sm text-gray-700">{itemA.text || `Item A ${index + 1}`}</p>
              </div>
              
              <ArrowRight className="h-4 w-4 text-gray-400" />
              
              <div className="flex-1">
                <Label className="text-sm font-medium">Match with:</Label>
                <Select
                  value={selectedItemBId}
                  onValueChange={(value) => handlePairChange(itemAId, value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select from Column B..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="__no_match__">-- No match --</SelectItem>
                    {columnB.map((itemB: any, bIndex: number) => {
                      const itemBId = getId(itemB);
                      if (!itemBId) {
                        console.warn('Column B item missing ID:', itemB);
                        return null;
                      }
                      
                      const isUsed = usedColumnBIds.has(itemBId) && selectedItemBId !== itemBId;
                      
                      return (
                        <SelectItem 
                          key={`${itemBId}-${bIndex}`} 
                          value={itemBId}
                          disabled={isUsed}
                        >
                          {itemB.text || `Item B ${bIndex + 1}`} {isUsed && '(already used)'}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>
            </div>
          );
        })}
      </div>

      <div className="flex items-center gap-2">
        <Badge variant={pairedCount === columnA.length ? "secondary" : "outline"}>
          {pairedCount} of {columnA.length} items paired
        </Badge>
      </div>

      {pairedCount > 0 && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            {pairedCount} matching pair(s) configured.
          </AlertDescription>
        </Alert>
      )}

      {pairedCount === 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Please create at least one matching pair.
          </AlertDescription>
        </Alert>
      )}

      <div className="text-xs text-gray-500 space-y-1">
        <p><strong>Note:</strong> Each item in Column A should be matched with exactly one item in Column B.</p>
        <p>Each item in Column B can only be used once (one-to-one matching).</p>
      </div>
    </div>
  );
}