'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, AlertTriangle } from 'lucide-react';

interface InputSolutionEditorProps {
  exerciseData: Record<string, any>;
  correctAnswer: any;
  onChange: (correctAnswer: any) => void;
}

export function InputSolutionEditor({
  exerciseData,
  correctAnswer,
  onChange
}: InputSolutionEditorProps) {
  const [answer, setAnswer] = useState<string>('');

  // Initialize answer from correct answer
  useEffect(() => {
    if (correctAnswer?.correctAnswer) {
      setAnswer(correctAnswer.correctAnswer);
    }
  }, [correctAnswer]);

  const handleAnswerChange = (value: string) => {
    setAnswer(value);
    onChange({
      correctAnswer: value
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 text-base font-semibold">
        <CheckCircle className="h-4 w-4" />
        Correct Answer
      </div>
          <p className="text-sm text-gray-600">
            Enter the correct answer for this input exercise:
          </p>
          
          <div className="space-y-2">
            <Label htmlFor="correct-answer">Correct Answer</Label>
            <Input
              id="correct-answer"
              value={answer}
              onChange={(e) => handleAnswerChange(e.target.value)}
              placeholder="Enter the correct answer..."
              className="w-full"
            />
          </div>

          {answer.trim() && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Students&apos; answers will be compared against: &ldquo;{answer}&rdquo;
              </AlertDescription>
            </Alert>
          )}

          {!answer.trim() && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Please enter the correct answer.
              </AlertDescription>
            </Alert>
          )}

          <div className="text-xs text-gray-500 space-y-1">
            <p><strong>Note:</strong> Answer comparison is case-sensitive and requires exact match.</p>
            <p>Consider adding multiple solution steps to explain the answer if needed.</p>
          </div>
    </div>
  );
}
