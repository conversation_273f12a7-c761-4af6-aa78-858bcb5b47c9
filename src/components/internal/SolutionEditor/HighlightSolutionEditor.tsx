'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertTriangle } from 'lucide-react';

interface HighlightSolutionEditorProps {
  exerciseData: Record<string, any>;
  correctAnswer: any;
  onChange: (correctAnswer: any) => void;
}

export function HighlightSolutionEditor({
  exerciseData,
  correctAnswer,
  onChange
}: HighlightSolutionEditorProps) {
  const [selectedIndices, setSelectedIndices] = useState<number[]>([]);

  const parts = exerciseData.parts || [];

  // Initialize selected indices from correct answer
  useEffect(() => {
    if (correctAnswer?.correctIndices && Array.isArray(correctAnswer.correctIndices)) {
      setSelectedIndices(correctAnswer.correctIndices);
    }
  }, [correctAnswer]);

  const handlePartToggle = (index: number) => {
    let newSelectedIndices: number[];
    
    if (selectedIndices.includes(index)) {
      newSelectedIndices = selectedIndices.filter(i => i !== index);
    } else {
      newSelectedIndices = [...selectedIndices, index];
    }
    
    setSelectedIndices(newSelectedIndices);
    onChange({
      correctIndices: newSelectedIndices
    });
  };

  if (!parts || parts.length === 0) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          No text parts found in exercise data. Please add text parts to the exercise first.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 text-base font-semibold">
        <CheckCircle className="h-4 w-4" />
        Highlight Correct Parts
      </div>
          <p className="text-sm text-gray-600">
            Click on the text parts that should be highlighted as correct:
          </p>
          
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex flex-wrap gap-1">
              {parts.map((part: string, index: number) => {
                const isSelected = selectedIndices.includes(index);
                
                return (
                  <Button
                    key={index}
                    variant={isSelected ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePartToggle(index)}
                    className={`text-sm ${
                      isSelected 
                        ? 'bg-yellow-400 hover:bg-yellow-500 text-black' 
                        : 'hover:bg-gray-100'
                    }`}
                  >
                    {part}
                  </Button>
                );
              })}
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Badge variant={selectedIndices.length > 0 ? "secondary" : "outline"}>
              {selectedIndices.length} part(s) selected
            </Badge>
          </div>

          {selectedIndices.length > 0 && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Selected parts will be highlighted as correct answers.
              </AlertDescription>
            </Alert>
          )}

          {selectedIndices.length === 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Please select at least one text part to highlight.
              </AlertDescription>
            </Alert>
          )}

          <div className="text-xs text-gray-500 space-y-1">
            <p><strong>Preview:</strong> Selected parts are shown with yellow highlighting above.</p>
            <p><strong>Note:</strong> Students will need to highlight the exact same parts to get the answer correct.</p>
          </div>
    </div>
  );
}
