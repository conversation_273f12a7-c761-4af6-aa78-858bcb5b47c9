'use client';

import { useState, useEffect } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertTriangle } from 'lucide-react';

interface MCMultiSolutionEditorProps {
  exerciseData: Record<string, any>;
  correctAnswer: any;
  onChange: (correctAnswer: any) => void;
}

export function MCMultiSolutionEditor({
  exerciseData,
  correctAnswer,
  onChange
}: MCMultiSolutionEditorProps) {
  const [selectedOptionIds, setSelectedOptionIds] = useState<string[]>([]);

  const options = exerciseData.options || [];

  // Initialize selected options from correct answer
  useEffect(() => {
    if (correctAnswer?.correctOptionIds && Array.isArray(correctAnswer.correctOptionIds)) {
      setSelectedOptionIds(correctAnswer.correctOptionIds);
    }
  }, [correctAnswer]);

  const handleOptionToggle = (optionId: string, checked: boolean) => {
    let newSelectedIds: string[];
    
    if (checked) {
      newSelectedIds = [...selectedOptionIds, optionId];
    } else {
      newSelectedIds = selectedOptionIds.filter(id => id !== optionId);
    }
    
    setSelectedOptionIds(newSelectedIds);
    onChange({
      correctOptionIds: newSelectedIds
    });
  };

  if (!options || options.length === 0) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          No options found in exercise data. Please add options to the exercise first.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 text-base font-semibold">
        <CheckCircle className="h-4 w-4" />
        Select Correct Answers
      </div>
          <p className="text-sm text-gray-600">
            Choose all correct answers from the available options:
          </p>
          
          <div className="space-y-3">
            {options.map((option: any, index: number) => {
              const optionId = option.public_id || option.publicId || `option-${index}`;
              const isSelected = selectedOptionIds.includes(optionId);
              
              return (
                <div
                  key={optionId}
                  className={`flex items-start space-x-3 p-3 rounded-lg border transition-colors ${
                    isSelected 
                      ? 'border-green-500 bg-green-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <Checkbox
                    id={optionId}
                    checked={isSelected}
                    onCheckedChange={(checked) => handleOptionToggle(optionId, checked as boolean)}
                    className="mt-1"
                  />
                  <Label htmlFor={optionId} className="flex-1 cursor-pointer">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        {option.image_url ? (
                          <div className="space-y-2">
                            <img 
                              src={option.image_url} 
                              alt={option.text || `Option ${index + 1}`}
                              className="max-w-32 max-h-32 object-contain rounded"
                            />
                            {option.text && (
                              <p className="text-sm">{option.text}</p>
                            )}
                          </div>
                        ) : (
                          <p className="text-sm">{option.text || `Option ${index + 1}`}</p>
                        )}
                      </div>
                      {isSelected && (
                        <Badge variant="secondary" className="ml-2">
                          Correct
                        </Badge>
                      )}
                    </div>
                  </Label>
                </div>
              );
            })}
          </div>

          {selectedOptionIds.length > 0 && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                {selectedOptionIds.length} option(s) selected as correct answers.
              </AlertDescription>
            </Alert>
          )}

          {selectedOptionIds.length === 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Please select at least one correct answer option.
              </AlertDescription>
            </Alert>
          )}
    </div>
  );
}
