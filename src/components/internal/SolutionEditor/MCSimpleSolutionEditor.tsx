'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertTriangle } from 'lucide-react';

interface MCSimpleSolutionEditorProps {
  exerciseData: Record<string, any>;
  correctAnswer: any;
  onChange: (correctAnswer: any) => void;
}

export function MCSimpleSolutionEditor({
  exerciseData,
  correctAnswer,
  onChange
}: MCSimpleSolutionEditorProps) {
  const [selectedOptionId, setSelectedOptionId] = useState<string>('');

  const options = exerciseData.options || [];

  // Initialize selected option from correct answer
  useEffect(() => {
    if (correctAnswer?.correctOptionId && Array.isArray(correctAnswer.correctOptionId)) {
      setSelectedOptionId(correctAnswer.correctOptionId[0] || '');
    }
  }, [correctAnswer]);

  const handleOptionSelect = (optionId: string) => {
    setSelectedOptionId(optionId);
    onChange({
      correctOptionId: [optionId]
    });
  };

  if (!options || options.length === 0) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          No options found in exercise data. Please add options to the exercise first.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 text-base font-semibold">
        <CheckCircle className="h-4 w-4" />
        Select Correct Answer
      </div>
          <p className="text-sm text-gray-600">
            Choose the correct answer from the available options:
          </p>
          
          <RadioGroup value={selectedOptionId} onValueChange={handleOptionSelect}>
            <div className="space-y-3">
              {options.map((option: any, index: number) => {
                const optionId = option.public_id || option.publicId || `option-${index}`;
                const isSelected = selectedOptionId === optionId;
                
                return (
                  <div
                    key={optionId}
                    className={`flex items-start space-x-3 p-3 rounded-lg border transition-colors ${
                      isSelected 
                        ? 'border-green-500 bg-green-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <RadioGroupItem value={optionId} id={optionId} className="mt-1" />
                    <Label htmlFor={optionId} className="flex-1 cursor-pointer">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          {option.image_url ? (
                            <div className="space-y-2">
                              <img 
                                src={option.image_url} 
                                alt={option.text || `Option ${index + 1}`}
                                className="max-w-32 max-h-32 object-contain rounded"
                              />
                              {option.text && (
                                <p className="text-sm">{option.text}</p>
                              )}
                            </div>
                          ) : (
                            <p className="text-sm">{option.text || `Option ${index + 1}`}</p>
                          )}
                        </div>
                        {isSelected && (
                          <Badge variant="secondary" className="ml-2">
                            Correct Answer
                          </Badge>
                        )}
                      </div>
                    </Label>
                  </div>
                );
              })}
            </div>
          </RadioGroup>

          {selectedOptionId && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Selected option will be marked as the correct answer for this exercise.
              </AlertDescription>
            </Alert>
          )}

          {!selectedOptionId && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Please select the correct answer option.
              </AlertDescription>
            </Alert>
          )}
    </div>
  );
}
