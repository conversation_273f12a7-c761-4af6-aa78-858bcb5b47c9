import { z } from 'zod';
import { createSchemaPair } from '@/schemas/schemaUtils';
import { 
  DraftExerciseStatusEnum, 
  ExerciseTypeEnum, 
  DifficultyEnum 
} from '@/types/internal/enums';
import { EditorRoleEnum } from '@/types/internal/authTypes';

// Publish Draft Request Schemas
const PublishDraftRequestAppSchema = z.object({
  publishNotes: z.string().optional(),
});

const PublishDraftRequestApiSchema = z.object({
  publish_notes: z.string().optional(),
});

export const PublishDraftRequestSchema = createSchemaPair(
  PublishDraftRequestAppSchema,
  PublishDraftRequestApiSchema
);

// Publish Draft Response Schemas
const PublishDraftResponseAppSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  draftId: z.number(),
  exerciseId: z.number(),
  publishedAt: z.string(),
});

const PublishDraftResponseApiSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  draft_id: z.number(),
  exercise_id: z.number(),
  published_at: z.string(),
});

export const PublishDraftResponseSchema = createSchemaPair(
  PublishDraftResponseAppSchema,
  PublishDraftResponseApiSchema
);

// Bulk Publish Request Schemas
const BulkPublishRequestAppSchema = z.object({
  draftIds: z.array(z.number()).min(1),
  batchName: z.string(),
  batchNotes: z.string().optional(),
});

const BulkPublishRequestApiSchema = z.object({
  draft_ids: z.array(z.number()).min(1),
  batch_name: z.string(),
  batch_notes: z.string().optional(),
});

export const BulkPublishRequestSchema = createSchemaPair(
  BulkPublishRequestAppSchema,
  BulkPublishRequestApiSchema
);

// Bulk Publish Response Schemas
const BulkPublishResponseAppSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  batchId: z.number(),
  totalDrafts: z.number(),
  successfulPublishes: z.number(),
  failedPublishes: z.number(),
  results: z.array(z.object({
    draftId: z.number(),
    success: z.boolean(),
    exerciseId: z.number().optional(),
    message: z.string(),
    error: z.string().optional(),
  })),
});

const BulkPublishResponseApiSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  batch_id: z.number(),
  total_drafts: z.number(),
  successful_publishes: z.number(),
  failed_publishes: z.number(),
  results: z.array(z.object({
    draft_id: z.number(),
    success: z.boolean(),
    exercise_id: z.number().optional(),
    message: z.string(),
    error: z.string().optional(),
  })),
});

export const BulkPublishResponseSchema = createSchemaPair(
  BulkPublishResponseAppSchema,
  BulkPublishResponseApiSchema
);

// Reject Draft Request Schemas
const RejectDraftRequestAppSchema = z.object({
  rejectionReason: z.string().min(1),
  suggestedChanges: z.array(z.string()).optional(),
});

const RejectDraftRequestApiSchema = z.object({
  rejection_reason: z.string().min(1),
  suggested_changes: z.array(z.string()).optional(),
});

export const RejectDraftRequestSchema = createSchemaPair(
  RejectDraftRequestAppSchema,
  RejectDraftRequestApiSchema
);

// Reject Draft Response Schemas
const RejectDraftResponseAppSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  draft: z.object({
    id: z.number(),
    publicId: z.string(),
    status: DraftExerciseStatusEnum,
    exerciseType: z.string(),
    difficulty: z.string().optional(),
    assignedEditor: z.object({
      id: z.number(),
      email: z.string(),
      role: z.string(),
    }).nullable().optional(),
    dataJson: z.record(z.any()),
    solutionJson: z.record(z.any()).optional(),
    media: z.array(z.any()).default([]),
    audit: z.array(z.any()).default([]),
    rejectReason: z.string().optional(),
    createdAt: z.string(),
    updatedAt: z.string(),
    publishedAt: z.string().nullable().optional(),
    learningNodes: z.array(z.object({
      title: z.string(),
      publicId: z.string(),
    })),
  }),
});

const RejectDraftResponseApiSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  draft: z.object({
    id: z.number(),
    public_id: z.string(),
    status: DraftExerciseStatusEnum,
    exercise_type: z.string(),
    difficulty: z.string().optional(),
    assigned_editor: z.object({
      id: z.number(),
      email: z.string(),
      role: z.string(),
    }).nullable().optional(),
    data_json: z.record(z.any()),
    solution_json: z.record(z.any()).optional(),
    media: z.array(z.any()).default([]),
    audit: z.array(z.any()).default([]),
    reject_reason: z.string().optional(),
    created_at: z.string(),
    updated_at: z.string(),
    published_at: z.string().nullable().optional(),
    learning_nodes: z.array(z.object({
      title: z.string(),
      public_id: z.string(),
    })),
  }),
});

export const RejectDraftResponseSchema = createSchemaPair(
  RejectDraftResponseAppSchema,
  RejectDraftResponseApiSchema
);



// Delete Draft Response Schemas
const DeleteDraftResponseAppSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  draftId: z.number(),
});

const DeleteDraftResponseApiSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  draft_id: z.number(),
});

export const DeleteDraftResponseSchema = createSchemaPair(
  DeleteDraftResponseAppSchema,
  DeleteDraftResponseApiSchema
);

// Get Users Request Schemas
const GetUsersRequestAppSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  role: EditorRoleEnum.optional(),
  search: z.string().optional(),
  isActive: z.boolean().optional(), // Add isActive filter parameter
});

const GetUsersRequestApiSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  role: EditorRoleEnum.optional(),
  search: z.string().optional(),
  is_active: z.boolean().optional(), // Add is_active filter parameter (snake_case for API)
});

export const GetUsersRequestSchema = createSchemaPair(
  GetUsersRequestAppSchema,
  GetUsersRequestApiSchema
);

// Editor schemas for individual editor
const EditorAppSchema = z.object({
  id: z.number(),
  publicId: z.string(),
  email: z.string().email(),
  role: EditorRoleEnum,
  scopes: z.array(z.object({
    id: z.number(),
    editorId: z.number(),
    scopeType: z.enum(['SUBJECT', 'CHAPTER', 'LEARNING_NODE']),
    scopeValue: z.string(),
    name: z.string().optional(),
    createdAt: z.string(),
  })),
  isActive: z.boolean(),
  createdAt: z.string(),
  lastLoginAt: z.string().optional(),
});

const EditorApiSchema = z.object({
  id: z.number(),
  public_id: z.string(),
  email: z.string().email(),
  role: EditorRoleEnum,
  scopes: z.array(z.object({
    id: z.number(),
    editor_id: z.number(),
    scope_type: z.enum(['SUBJECT', 'CHAPTER', 'LEARNING_NODE']),
    scope_value: z.string(),
    name: z.string().optional(),
    created_at: z.string(),
  })),
  is_active: z.boolean(),
  created_at: z.string(),
  last_login_at: z.string().nullable().optional(),
});

// Get Users Response Schemas - Updated to match backend field names after camelCase conversion
const GetUsersResponseAppSchema = z.object({
  editors: z.array(EditorAppSchema), // Match backend field name
  total: z.number(), // Match backend field name
  page: z.number(), // Match backend field name
  limit: z.number(), // Match backend field name
  totalPages: z.number(), // Backend total_pages becomes totalPages
});

const GetUsersResponseApiSchema = z.object({
  editors: z.array(EditorApiSchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  total_pages: z.number(),
});

// Schema pair for individual editor
export const EditorSchema = createSchemaPair(
  EditorAppSchema,
  EditorApiSchema
);

// Use standard createSchemaPair - customFetch handles automatic transformation
export const GetUsersResponseSchema = createSchemaPair(
  GetUsersResponseAppSchema,
  GetUsersResponseApiSchema
);

// Create User Request Schemas
const CreateUserRequestAppSchema = z.object({
  email: z.string().email(),
  role: EditorRoleEnum,
  isActive: z.boolean().default(true),
  password: z.string().min(8).optional(), // Optional password field for admin to set
});

const CreateUserRequestApiSchema = z.object({
  email: z.string().email(),
  role: EditorRoleEnum,
  is_active: z.boolean().default(true),
  password: z.string().min(8).optional(), // Optional password field for admin to set
});

export const CreateUserRequestSchema = createSchemaPair(
  CreateUserRequestAppSchema,
  CreateUserRequestApiSchema
);

// Create User Response Schemas
// Updated to match backend EditorAccountResponse schema
const CreateUserResponseAppSchema = z.object({
  id: z.number(),
  publicId: z.string(),
  email: z.string().email(),
  role: EditorRoleEnum,
  isActive: z.boolean(),
  createdAt: z.string(),
  lastLoginAt: z.string().nullable().optional(),
  totalDraftsReviewed: z.number(),
  totalDraftsAccepted: z.number(),
  scopes: z.array(z.record(z.any())),
});

const CreateUserResponseApiSchema = z.object({
  id: z.number(),
  public_id: z.string(),
  email: z.string().email(),
  role: EditorRoleEnum,
  is_active: z.boolean(),
  created_at: z.string(),
  last_login_at: z.string().nullable().optional(),
  total_drafts_reviewed: z.number(),
  total_drafts_accepted: z.number(),
  scopes: z.array(z.record(z.any())),
});

export const CreateUserResponseSchema = createSchemaPair(
  CreateUserResponseAppSchema,
  CreateUserResponseApiSchema
);

// Update User Request Schemas
const UpdateUserRequestAppSchema = z.object({
  email: z.string().email(),
  isActive: z.boolean().optional(),
  role: EditorRoleEnum.optional(),
});

const UpdateUserRequestApiSchema = z.object({
  email: z.string().email(),
  is_active: z.boolean().optional(),
  role: EditorRoleEnum.optional(),
});

export const UpdateUserRequestSchema = createSchemaPair(
  UpdateUserRequestAppSchema,
  UpdateUserRequestApiSchema
);

// Update User Response Schemas - Backend returns full editor object
const UpdateUserResponseAppSchema = z.object({
  id: z.number(),
  publicId: z.string(),
  email: z.string().email(),
  role: EditorRoleEnum,
  isActive: z.boolean(),
  createdAt: z.string(),
  lastLoginAt: z.string().nullable().optional(),
  totalDraftsReviewed: z.number(),
  totalDraftsAccepted: z.number(),
  scopes: z.array(z.record(z.any())),
});

const UpdateUserResponseApiSchema = z.object({
  id: z.number(),
  public_id: z.string(),
  email: z.string().email(),
  role: EditorRoleEnum,
  is_active: z.boolean(),
  created_at: z.string(),
  last_login_at: z.string().nullable().optional(),
  total_drafts_reviewed: z.number(),
  total_drafts_accepted: z.number(),
  scopes: z.array(z.record(z.any())),
});

export const UpdateUserResponseSchema = createSchemaPair(
  UpdateUserResponseAppSchema,
  UpdateUserResponseApiSchema
);

// Delete User Response Schemas
const DeleteUserResponseAppSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  editorPublicId: z.string(),
});

const DeleteUserResponseApiSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  editor_public_id: z.string(),
});

export const DeleteUserResponseSchema = createSchemaPair(
  DeleteUserResponseAppSchema,
  DeleteUserResponseApiSchema
);

// Add Scope Request Schemas
const AddScopeRequestAppSchema = z.object({
  scopeType: z.enum(['SUBJECT', 'CHAPTER', 'LEARNING_NODE']),
  scopeValue: z.string(),
});

const AddScopeRequestApiSchema = z.object({
  scope_type: z.enum(['SUBJECT', 'CHAPTER', 'LEARNING_NODE']),
  scope_value: z.string(),
});

export const AddScopeRequestSchema = createSchemaPair(
  AddScopeRequestAppSchema,
  AddScopeRequestApiSchema
);

// Add Scope Response Schemas
const AddScopeResponseAppSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  scope: z.object({
    id: z.number(),
    editorId: z.number(),
    type: z.enum(['SUBJECT', 'CHAPTER', 'LEARNING_NODE']),
    name: z.string(),
    publicId: z.string(),
  }),
});

const AddScopeResponseApiSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  scope: z.object({
    id: z.number(),
    editor_id: z.number(),
    type: z.enum(['SUBJECT', 'CHAPTER', 'LEARNING_NODE']),
    name: z.string(),
    public_id: z.string(),
  }),
});

export const AddScopeResponseSchema = createSchemaPair(
  AddScopeResponseAppSchema,
  AddScopeResponseApiSchema
);

// System Stats Response Schemas
const SystemStatsResponseAppSchema = z.object({
  totalDrafts: z.number(),
  draftsInReview: z.number(),
  publishedExercises: z.number(),
  activeUsers: z.number(),
  totalMedia: z.number(),
  storageUsed: z.string(),
  recentActivity: z.object({
    draftsCreatedToday: z.number(),
    draftsPublishedToday: z.number(),
    mediaUploadedToday: z.number(),
  }),
});

const SystemStatsResponseApiSchema = z.object({
  total_drafts: z.number(),
  drafts_in_review: z.number(),
  published_exercises: z.number(),
  active_users: z.number(),
  total_media: z.number(),
  storage_used: z.string(),
  recent_activity: z.object({
    drafts_created_today: z.number(),
    drafts_published_today: z.number(),
    media_uploaded_today: z.number(),
  }),
});

export const SystemStatsResponseSchema = createSchemaPair(
  SystemStatsResponseAppSchema,
  SystemStatsResponseApiSchema
);

// Review Queue Request Schemas
const ReviewQueueRequestAppSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  priority: z.enum(['low', 'medium', 'high']).optional(),
  exerciseType: ExerciseTypeEnum.optional(),
});

const ReviewQueueRequestApiSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  priority: z.enum(['low', 'medium', 'high']).optional(),
  exercise_type: ExerciseTypeEnum.optional(),
});

export const ReviewQueueRequestSchema = createSchemaPair(
  ReviewQueueRequestAppSchema,
  ReviewQueueRequestApiSchema
);

// Review Queue Response Schemas
const ReviewQueueResponseAppSchema = z.object({
  drafts: z.array(z.object({
    id: z.number(),
    publicId: z.string(),
    exerciseType: ExerciseTypeEnum,
    difficulty: DifficultyEnum,
    status: DraftExerciseStatusEnum,
    submittedAt: z.string(),
    submittedBy: z.object({
      id: z.number(),
      email: z.string(),
    }),
    priority: z.enum(['low', 'medium', 'high']),
    reviewNotes: z.string().optional(),
  })),
  totalCount: z.number(),
  currentPage: z.number(),
  totalPages: z.number(),
});

const ReviewQueueResponseApiSchema = z.object({
  drafts: z.array(z.object({
    id: z.number(),
    public_id: z.string(),
    exercise_type: ExerciseTypeEnum,
    difficulty: DifficultyEnum,
    status: DraftExerciseStatusEnum,
    submitted_at: z.string(),
    submitted_by: z.object({
      id: z.number(),
      email: z.string(),
    }),
    priority: z.enum(['low', 'medium', 'high']),
    review_notes: z.string().optional(),
  })),
  total_count: z.number(),
  current_page: z.number(),
  total_pages: z.number(),
});

export const ReviewQueueResponseSchema = createSchemaPair(
  ReviewQueueResponseAppSchema,
  ReviewQueueResponseApiSchema
);





// Metrics Overview Request Schemas
const MetricsOverviewRequestAppSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  groupBy: z.enum(['day', 'week', 'month']).optional(),
});

const MetricsOverviewRequestApiSchema = z.object({
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  group_by: z.enum(['day', 'week', 'month']).optional(),
});

export const MetricsOverviewRequestSchema = createSchemaPair(
  MetricsOverviewRequestAppSchema,
  MetricsOverviewRequestApiSchema
);

// Metrics Overview Response Schemas
const MetricsOverviewResponseAppSchema = z.object({
  period: z.object({
    start: z.string(),
    end: z.string(),
  }),
  draftMetrics: z.object({
    totalCreated: z.number(),
    totalClaimed: z.number(),
    totalAccepted: z.number(),
    totalRejected: z.number(),
    totalPublished: z.number(),
    averageTimeToClaimHours: z.number(),
    averageTimeToCompleteHours: z.number(),
    averageTimeToPublishHours: z.number(),
  }),
  editorMetrics: z.object({
    activeEditors: z.number(),
    totalAssignments: z.number(),
    averageDraftsPerEditor: z.number(),
    topPerformers: z.array(z.object({
      editorId: z.number(),
      email: z.string(),
      draftsCompleted: z.number(),
      acceptanceRate: z.number(),
    })),
  }),
  publishingMetrics: z.object({
    totalPublished: z.number(),
    bySubject: z.record(z.number()),
    byExerciseType: z.record(z.number()),
  }),
});

const MetricsOverviewResponseApiSchema = z.object({
  period: z.object({
    start: z.string(),
    end: z.string(),
  }),
  draft_metrics: z.object({
    total_created: z.number(),
    total_claimed: z.number(),
    total_accepted: z.number(),
    total_rejected: z.number(),
    total_published: z.number(),
    average_time_to_claim_hours: z.number(),
    average_time_to_complete_hours: z.number(),
    average_time_to_publish_hours: z.number(),
  }),
  editor_metrics: z.object({
    active_editors: z.number(),
    total_assignments: z.number(),
    average_drafts_per_editor: z.number(),
    top_performers: z.array(z.object({
      editor_id: z.number(),
      email: z.string(),
      drafts_completed: z.number(),
      acceptance_rate: z.number(),
    })),
  }),
  publishing_metrics: z.object({
    total_published: z.number(),
    by_subject: z.record(z.number()),
    by_exercise_type: z.record(z.number()),
  }),
});

export const MetricsOverviewResponseSchema = createSchemaPair(
  MetricsOverviewResponseAppSchema,
  MetricsOverviewResponseApiSchema
);

// Content Subject Schema
const ContentSubjectAppSchema = z.object({
  id: z.string(),
  name: z.string(),
  publicId: z.string(),
  totalChapters: z.number(),
  totalLearningNodes: z.number(),
});

const ContentSubjectApiSchema = z.object({
  id: z.string(),
  name: z.string(),
  public_id: z.string(),
  total_chapters: z.number(),
  total_learning_nodes: z.number(),
});

const ContentSubjectsResponseAppSchema = z.object({
  subjects: z.array(ContentSubjectAppSchema),
});

const ContentSubjectsResponseApiSchema = z.object({
  subjects: z.array(ContentSubjectApiSchema),
});

export const ContentSubjectsResponseSchema = createSchemaPair(
  ContentSubjectsResponseAppSchema,
  ContentSubjectsResponseApiSchema
);

// Subject Chapters Schema
const ChapterInfoAppSchema = z.object({
  id: z.string(),
  title: z.string(),
  publicId: z.string(),
  order: z.number().nullable().optional(),
  totalLearningNodes: z.number(),
});

const ChapterInfoApiSchema = z.object({
  id: z.string(),
  title: z.string(),
  public_id: z.string(),
  order: z.number().nullable().optional(),
  total_learning_nodes: z.number(),
});

const SubjectChaptersResponseAppSchema = z.object({
  subject: z.object({
    id: z.string(),
    name: z.string(),
  }),
  chapters: z.array(ChapterInfoAppSchema),
});

const SubjectChaptersResponseApiSchema = z.object({
  subject: z.object({
    id: z.string(),
    name: z.string(),
  }),
  chapters: z.array(ChapterInfoApiSchema),
});

export const SubjectChaptersResponseSchema = createSchemaPair(
  SubjectChaptersResponseAppSchema,
  SubjectChaptersResponseApiSchema
);

// Chapter Learning Nodes Schema
const LearningNodeInfoAppSchema = z.object({
  id: z.string(),
  title: z.string(),
  publicId: z.string(),
  order: z.number().nullable().optional(),
});

const LearningNodeInfoApiSchema = z.object({
  id: z.string(),
  title: z.string(),
  public_id: z.string(),
  order: z.number().nullable().optional(),
});

const ChapterLearningNodesResponseAppSchema = z.object({
  chapter: z.object({
    id: z.string(),
    title: z.string(),
    subjectName: z.string(),
  }),
  learningNodes: z.array(LearningNodeInfoAppSchema),
});

const ChapterLearningNodesResponseApiSchema = z.object({
  chapter: z.object({
    id: z.string(),
    title: z.string(),
    subject_name: z.string(),
  }),
  learning_nodes: z.array(LearningNodeInfoApiSchema),
});

export const ChapterLearningNodesResponseSchema = createSchemaPair(
  ChapterLearningNodesResponseAppSchema,
  ChapterLearningNodesResponseApiSchema
);

// Export types
export type PublishDraftRequestApp = z.infer<typeof PublishDraftRequestSchema.frontend>;
export type PublishDraftResponseApp = z.infer<typeof PublishDraftResponseSchema.frontend>;
export type BulkPublishRequestApp = z.infer<typeof BulkPublishRequestSchema.frontend>;
export type BulkPublishResponseApp = z.infer<typeof BulkPublishResponseSchema.frontend>;
export type RejectDraftRequestApp = z.infer<typeof RejectDraftRequestSchema.frontend>;
export type RejectDraftResponseApp = z.infer<typeof RejectDraftResponseSchema.frontend>;

export type DeleteDraftResponseApp = z.infer<typeof DeleteDraftResponseSchema.frontend>;
export type GetUsersRequestApp = z.infer<typeof GetUsersRequestSchema.frontend>;
export type GetUsersResponseApp = z.infer<typeof GetUsersResponseSchema.frontend>;
export type CreateUserRequestApp = z.infer<typeof CreateUserRequestSchema.frontend>;
export type CreateUserResponseApp = z.infer<typeof CreateUserResponseSchema.frontend>;
export type UpdateUserRequestApp = z.infer<typeof UpdateUserRequestSchema.frontend>;
export type UpdateUserResponseApp = z.infer<typeof UpdateUserResponseSchema.frontend>;
export type DeleteUserResponseApp = z.infer<typeof DeleteUserResponseSchema.frontend>;
export type SystemStatsResponseApp = z.infer<typeof SystemStatsResponseSchema.frontend>;
export type ReviewQueueRequestApp = z.infer<typeof ReviewQueueRequestSchema.frontend>;
export type ReviewQueueResponseApp = z.infer<typeof ReviewQueueResponseSchema.frontend>;

export type MetricsOverviewRequestApp = z.infer<typeof MetricsOverviewRequestSchema.frontend>;
export type MetricsOverviewResponseApp = z.infer<typeof MetricsOverviewResponseSchema.frontend>;
export type AddScopeRequestApp = z.infer<typeof AddScopeRequestSchema.frontend>;
export type AddScopeResponseApp = z.infer<typeof AddScopeResponseSchema.frontend>;
export type EditorApp = z.infer<typeof EditorSchema.frontend>;
export type ContentSubjectsResponseApp = z.infer<typeof ContentSubjectsResponseSchema.frontend>;
export type SubjectChaptersResponseApp = z.infer<typeof SubjectChaptersResponseSchema.frontend>;
export type ChapterLearningNodesResponseApp = z.infer<typeof ChapterLearningNodesResponseSchema.frontend>;

// Review Queue is just drafts with specific status, so we can reuse the ReviewQueueResponseSchema
export const GetReviewQueueResponseSchema = ReviewQueueResponseSchema;