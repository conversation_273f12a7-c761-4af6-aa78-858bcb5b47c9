import { z } from 'zod';

// Base exercise data schema matching published exercise structure
const BaseExerciseDataSchema = z.object({
  prompt: z.string(),
  prompt_image_url: z.string().nullish().optional(), // Changed from prompt_image_public_id to match production
});

// Exercise option schema
const ExerciseOptionSchema = z.object({
  public_id: z.string(),
  text: z.string(),
});

const ExerciseImageOptionSchema = z.object({
  public_id: z.string(),
  image_url: z.string(), // Changed from image_public_id to match production
  text: z.string().nullish().optional(), // Changed to match production nullish
});

// Multiple Choice Exercise Data
const MCExerciseDataSchema = BaseExerciseDataSchema.extend({
  options: z.array(z.union([ExerciseOptionSchema, ExerciseImageOptionSchema])),
});

// Input Exercise Data
const InputExerciseDataSchema = BaseExerciseDataSchema;

// Cloze Exercise Data
const ClozeExerciseDataSchema = BaseExerciseDataSchema.extend({
  text_parts: z.array(z.string()).default([]),
  hints: z.array(z.string().optional()).optional(),
});

// Dropdown Exercise Data
const DropdownExerciseDataSchema = BaseExerciseDataSchema.extend({
  template: z.string(),
  options: z.array(z.array(z.string())),
});

// Highlight Exercise Data
const HighlightExerciseDataSchema = BaseExerciseDataSchema.extend({
  parts: z.array(z.string()).default([]),
});

// Matching Pairs Data
const MatchingPairItemSchema = z.object({
  public_id: z.string(),
  text: z.string(),
});

const MatchingPairsExerciseDataSchema = BaseExerciseDataSchema.extend({
  column_a: z.array(MatchingPairItemSchema).default([]),
  column_b: z.array(MatchingPairItemSchema).default([]),
});

// Categorize Exercise Data
const CategorizeItemSchema = z.object({
  public_id: z.string(),
  text: z.string(),
});

const CategoryItemSchema = z.object({
  public_id: z.string(),
  text: z.string(),
});

const CategorizeExerciseDataSchema = BaseExerciseDataSchema.extend({
  categories: z.array(CategoryItemSchema).default([]),
  options: z.array(CategorizeItemSchema).default([]),
});

// Error Correction Exercise Data
const ErrorCorrectionExerciseDataSchema = BaseExerciseDataSchema.extend({
  parts: z.array(z.string()).default([]),
});

// True/False Exercise Data
const TrueFalseExerciseDataSchema = BaseExerciseDataSchema;

// Solution Schemas
const SolutionStepSchema = z.object({
  text: z.string().optional(),
  math: z.string().nullable().optional(),
  imagePublicId: z.string().nullable().optional(),
});

const MCSimpleSolutionAnswerSchema = z.object({
  correct_option_id: z.array(z.string()),
});

const MCMultiSolutionAnswerSchema = z.object({
  correct_option_ids: z.array(z.string()),
});

const InputSolutionAnswerSchema = z.object({
  correct_answer: z.string(),
});

const ClozeSolutionAnswerSchema = z.object({
  correct_answers: z.array(z.string()),
});

const DropdownSolutionAnswerSchema = z.object({
  correct_selections: z.array(z.string()),
});

const HighlightSolutionAnswerSchema = z.object({
  correct_indices: z.array(z.number()), // Changed from correct_highlights to match production
});

const MatchingPairsSolutionAnswerSchema = z.object({
  correct_pairs: z.record(z.string()), // Changed to Dict[str, str] format to match production
});

const CategorizeSolutionAnswerSchema = z.object({
  correct_categories: z.record(z.string()), // Changed from correct_categorization to match production
});

const ErrorCorrectionTextSchema = z.object({
  index: z.number(),
  text: z.string(),
});

const ErrorCorrectionSolutionAnswerSchema = z.object({
  corrections: z.array(ErrorCorrectionTextSchema), // Changed from correct_text to match production
});

const TrueFalseSolutionAnswerSchema = z.object({
  is_true: z.boolean(),
});

// Combined solution answer schema
const SolutionAnswerSchema = z.union([
  MCSimpleSolutionAnswerSchema,
  MCMultiSolutionAnswerSchema,
  InputSolutionAnswerSchema,
  ClozeSolutionAnswerSchema,
  DropdownSolutionAnswerSchema,
  HighlightSolutionAnswerSchema,
  MatchingPairsSolutionAnswerSchema,
  CategorizeSolutionAnswerSchema,
  ErrorCorrectionSolutionAnswerSchema,
  TrueFalseSolutionAnswerSchema,
]);

const ExerciseSolutionSchema = z.object({
  correctAnswer: SolutionAnswerSchema,
  solutionSteps: z.array(SolutionStepSchema).nullable().optional(),
  videoPublicId: z.string().nullable().optional(),
});

// Exercise type to data schema mapping
export const exerciseDataSchemaMap = {
  'mc-simple': MCExerciseDataSchema,
  'mc-multi': MCExerciseDataSchema,
  'input': InputExerciseDataSchema,
  'cloze': ClozeExerciseDataSchema,
  'dropdown': DropdownExerciseDataSchema,
  'highlight': HighlightExerciseDataSchema,
  'matching-pairs': MatchingPairsExerciseDataSchema,
  'categorize': CategorizeExerciseDataSchema,
  'error-correction': ErrorCorrectionExerciseDataSchema,
  'true-false': TrueFalseExerciseDataSchema,
} as const;

// Validation function for exercise data
export function validateExerciseData(exerciseType: keyof typeof exerciseDataSchemaMap, data: any) {
  const schema = exerciseDataSchemaMap[exerciseType];
  if (!schema) {
    throw new Error(`Unknown exercise type: ${exerciseType}`);
  }
  return schema.parse(data);
}

// Validation function for exercise solution
export function validateExerciseSolution(data: any) {
  return ExerciseSolutionSchema.parse(data);
}

// Export individual schemas for specific use cases
export {
  MCExerciseDataSchema,
  InputExerciseDataSchema,
  ClozeExerciseDataSchema,
  DropdownExerciseDataSchema,
  HighlightExerciseDataSchema,
  MatchingPairsExerciseDataSchema,
  CategorizeExerciseDataSchema,
  ErrorCorrectionExerciseDataSchema,
  TrueFalseExerciseDataSchema,
  ExerciseSolutionSchema,
};
