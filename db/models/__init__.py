# models/__init__.py (Example - ensures models are registered)
# --- Associations ---
from .associations.learning_node_exercise import LearningNodeExerciseAssociation
from .associations.child_account_exercise import ChildAccountExerciseAssociation, ExerciseStatusEnum

# --- Main Models ---
from .account import Account, ChildAccount, ParentChildAssignment, LanguageEnum
from .analytics import SignUp
from .app_related import Toast, DismissedToast, TaskInvocation
from .auth import AccountAuthSecurity, PinResetRequest, EmailRateLimit
from .content import Year, Chapter, Subject
from .exercise import (
    Exercise, MCExercise, InputExercise, ClozeExercise, ErrorCorrectionExercise,
    ExerciseFactory, ExerciseOptionModel, ExerciseSolutionModel, InputSolutionAnswer,
    MCSimpleSolutionAnswer, InputExerciseData, ExerciseTypeEnum, DifficultyEnum, BaseExerciseData,
    MCExerciseData, ClozeExerciseData, DropdownExerciseData, HighlightExerciseData,
    MatchingPairsExerciseData, CategorizeExerciseData, ErrorCorrectionExerciseData,
    SolutionStepModel, MCMultiSolutionAnswer, DropdownSolutionAnswer, HighlightSolutionAnswer,
    MatchingPairsSolutionAnswer, CategorizeSolutionAnswer, ErrorCorrectionSolutionAnswer, SolutionAnswerType
)
from .media import AudioFile, AudioFileTypeEnum, VideoFile, VideoFileTypeEnum, ImageFile, ImageFileTypeEnum
from .learning_node import (
    LearningNode, LearningNodeFactory, LearningNodeRelationship, LearningNodeTypeEnum,
    VocabWordModel, VocabGroupModel, VocabContentModel, VerbFormModel, VerbConjugationModel,
    VerbModel, VerbTenseModel, VerbMoodModel, VerbContentModel, ReadingExplanationModel,
    ReadingContentModel, ListeningContentModel, MathContentModel, GrammarContentModel, NodeContentType
)
from .other import SummaryDownload, YearSummaryDownload
from .regulation import CookiesConsent, MarketingConsent
# Updated subscription imports
from .subscription import (
    PriceVersion, PriceEligibility, ActiveSubscription, DiscountCode,
    DiscountCodeUsage, Trial, SubscriptionStatusType, PlanTypeEnum, BillingPeriodEnum,
    SubscriptionOption, ActiveSubscriptionPlanLink,
    PlanSelectedSubject, DiscountApplicabilityType, StripeCheckoutSessionData,
    SubscriptionPause, SubscriptionPauseStatus
)
from .subscription_changes import (
    SubscriptionPendingChange, SubscriptionPendingSelectedSubject,
    SubscriptionChangeLog, SubscriptionChangeType, SubscriptionChangeStatus
)
from .contact_submission import ContactSubmission
from .landing import BlogPost

# --- Editorial Models ---
from .editorial import EditorAccount, EditorScope, EditorRole
from .draft_exercise import DraftExercise, DraftExerciseStatus
from .draft_media import DraftMediaFile, DraftMediaType
from .audit import PublishBatch


# Update __all__ if you use it
__all__ = [
    "LearningNodeExerciseAssociation", "ChildAccountExerciseAssociation", "ExerciseStatusEnum",
    "Account", "ChildAccount", "ParentChildAssignment", "LanguageEnum",
    "SignUp", "Toast", "DismissedToast",
    "TaskInvocation", "AudioFile", "AudioFileTypeEnum", "VideoFile", "VideoFileTypeEnum", "ImageFile", "ImageFileTypeEnum", "AccountAuthSecurity", "PinResetRequest", "EmailRateLimit",
    "Year", "Chapter", "Subject",
    "Exercise", "MCExercise", "InputExercise", "ClozeExercise",
    "ErrorCorrectionExercise", "ExerciseFactory", "ExerciseOptionModel", "ExerciseSolutionModel",
    "InputSolutionAnswer", "MCSimpleSolutionAnswer", "InputExerciseData", "ExerciseTypeEnum", "DifficultyEnum",
    "BaseExerciseData", "MCExerciseData", "ClozeExerciseData", "DropdownExerciseData",
    "HighlightExerciseData", "MatchingPairsExerciseData", "CategorizeExerciseData",
    "ErrorCorrectionExerciseData", "SolutionStepModel", "MCMultiSolutionAnswer",
    "DropdownSolutionAnswer", "HighlightSolutionAnswer", "MatchingPairsSolutionAnswer",
    "CategorizeSolutionAnswer", "ErrorCorrectionSolutionAnswer", "SolutionAnswerType",
    "LearningNode", "LearningNodeFactory", "LearningNodeRelationship", "LearningNodeTypeEnum",
    "VocabWordModel", "VocabGroupModel", "VocabContentModel", "VerbFormModel", "VerbConjugationModel",
    "VerbModel", "VerbTenseModel", "VerbMoodModel", "VerbContentModel", "ReadingExplanationModel",
    "ReadingContentModel", "ListeningContentModel", "MathContentModel", "GrammarContentModel", "NodeContentType",
    "SummaryDownload", "YearSummaryDownload",
    "CookiesConsent", "MarketingConsent",
    # Subscription related
    "PriceVersion", "PriceEligibility", "ActiveSubscription", "DiscountCode", "DiscountCodeUsage", "Trial",
    "SubscriptionStatusType", "PlanTypeEnum", "BillingPeriodEnum", "DiscountApplicabilityType",
    "SubscriptionOption", "ActiveSubscriptionPlanLink", "PlanSelectedSubject",
    "StripeCheckoutSessionData", "SubscriptionPause", "SubscriptionPauseStatus",
    # Subscription changes
    "SubscriptionPendingChange", "SubscriptionPendingSelectedSubject",
    "SubscriptionChangeLog", "SubscriptionChangeType", "SubscriptionChangeStatus",
    "ContactSubmission",
    "BlogPost",
    # Editorial models
    "EditorAccount", "EditorScope", "EditorRole",
    "DraftExercise", "DraftExerciseStatus",
    "DraftMediaFile", "DraftMediaType",
    "PublishBatch",
]