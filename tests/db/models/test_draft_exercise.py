import pytest
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from datetime import datetime, UTC
import uuid

from db.models import (
    DraftExercise, DraftExerciseStatus,
    EditorAccount, EditorRole, Exercise, LearningNode,
    LearningNodeFactory, LearningNodeTypeEnum
)
from tests.fixtures.editorial_fixtures import EditorFixtures, DraftFixtures

class TestDraftExercise:

    def _create_test_learning_node(self, db_session: Session) -> LearningNode:
        """Helper method to create a test learning node"""
        node = LearningNodeFactory.create_node(
            node_type=LearningNodeTypeEnum.GRAMMAR.value,
            public_id=f"ln_{uuid.uuid4().hex[:8]}",
            title="Test Node",
            content={"video_public_id": None, "notes": "Test"}
        )
        db_session.add(node)
        db_session.commit()
        return node
    """Test DraftExercise model"""
    
    def test_create_draft_exercise(self, db_session: Session):
        """Test valid draft creation with required fields"""
        # Create a learning node first
        node = LearningNodeFactory.create_node(
            node_type=LearningNodeTypeEnum.GRAMMAR.value,
            public_id=f"ln_{uuid.uuid4().hex[:8]}",
            title="Test Node",
            content={"video_public_id": None, "notes": "Test"}
        )
        db_session.add(node)
        db_session.commit()

        draft = DraftFixtures.create_draft_exercise(
            db_session,
            public_id="draft_123",
            exercise_type="mc-simple",
            difficulty="medium",
            learning_node=node,
            data={
                "prompt": "What is 2+2?",
                "options": [
                    {"public_id": "opt1", "text": "3"},
                    {"public_id": "opt2", "text": "4"}
                ]
            }
        )
        
        assert draft.id is not None
        assert draft.public_id == "draft_123"
        assert draft.exercise_type == "mc-simple"
        assert draft.difficulty == "medium"
        assert draft.data_json["prompt"] == "What is 2+2?"
        assert draft.status == DraftExerciseStatus.NEW
        assert draft.created_at is not None
        assert draft.updated_at is not None
        assert draft.learning_node_id == node.id
        assert draft.learning_node == node
    
    def test_status_defaults_to_new(self, db_session: Session):
        """Test status defaults to NEW"""
        # Create a learning node first
        node = LearningNodeFactory.create_node(
            node_type=LearningNodeTypeEnum.GRAMMAR.value,
            public_id=f"ln_{uuid.uuid4().hex[:8]}",
            title="Test Node",
            content={"video_public_id": None, "notes": "Test"}
        )
        db_session.add(node)
        db_session.commit()

        draft = DraftFixtures.create_draft_exercise(db_session, learning_node=node)
        assert draft.status == DraftExerciseStatus.NEW
    
    def test_json_fields_properly_stored(self, db_session: Session):
        """Test JSON fields properly stored"""
        # Create a learning node first
        node = LearningNodeFactory.create_node(
            node_type=LearningNodeTypeEnum.GRAMMAR.value,
            public_id=f"ln_{uuid.uuid4().hex[:8]}",
            title="Test Node",
            content={"video_public_id": None, "notes": "Test"}
        )
        db_session.add(node)
        db_session.commit()

        complex_data = {
            "prompt": "Complex question",
            "options": [
                {"public_id": "opt1", "text": "Option 1", "image_public_id": "img1"},
                {"public_id": "opt2", "text": "Option 2"}
            ],
            "metadata": {"difficulty_score": 0.7}
        }

        draft = DraftFixtures.create_draft_exercise(
            db_session,
            learning_node=node,
            data=complex_data
        )
        
        # Verify data is stored and retrieved correctly
        assert draft.data_json == complex_data
        assert draft.data_json["metadata"]["difficulty_score"] == 0.7
    
    def test_draft_status_transitions(self, db_session: Session):
        """Test valid status transitions"""
        # Create a learning node first
        node = LearningNodeFactory.create_node(
            node_type=LearningNodeTypeEnum.GRAMMAR.value,
            public_id=f"ln_{uuid.uuid4().hex[:8]}",
            title="Test Node",
            content={"video_public_id": None, "notes": "Test"}
        )
        db_session.add(node)
        db_session.commit()

        editor = EditorFixtures.create_editor_account(db_session)
        draft = DraftFixtures.create_draft_exercise(db_session, learning_node=node)
        
        # NEW → IN_REVIEW (valid)
        draft.status = DraftExerciseStatus.IN_REVIEW
        draft.assigned_editor_id = editor.id
        db_session.commit()
        assert draft.status == DraftExerciseStatus.IN_REVIEW
        
        # IN_REVIEW → ACCEPTED_BY_EDITOR (valid)
        draft.status = DraftExerciseStatus.ACCEPTED_BY_EDITOR
        db_session.commit()
        assert draft.status == DraftExerciseStatus.ACCEPTED_BY_EDITOR
        
        # ACCEPTED_BY_EDITOR → PUBLISHED (valid)
        draft.status = DraftExerciseStatus.PUBLISHED
        draft.published_at = datetime.now(UTC)
        db_session.commit()
        assert draft.status == DraftExerciseStatus.PUBLISHED
    
    def test_draft_status_rejection_flow(self, db_session: Session):
        """Test rejection and re-review flow"""
        node = self._create_test_learning_node(db_session)
        editor = EditorFixtures.create_editor_account(db_session)
        draft = DraftFixtures.create_draft_exercise(
            db_session,
            learning_node=node,
            status=DraftExerciseStatus.ACCEPTED_BY_EDITOR,
            assigned_editor=editor
        )
        
        # ACCEPTED_BY_EDITOR → REJECTED_BY_ADMIN (valid)
        draft.status = DraftExerciseStatus.REJECTED_BY_ADMIN
        draft.reject_reason = "Needs improvement"
        db_session.commit()
        assert draft.status == DraftExerciseStatus.REJECTED_BY_ADMIN
        assert draft.reject_reason == "Needs improvement"
        
        # REJECTED_BY_ADMIN → IN_REVIEW (valid)
        draft.status = DraftExerciseStatus.IN_REVIEW
        draft.reject_reason = None
        db_session.commit()
        assert draft.status == DraftExerciseStatus.IN_REVIEW
        assert draft.reject_reason is None
    
    def test_draft_editor_assignment(self, db_session: Session):
        """Test editor assignment to drafts"""
        node = self._create_test_learning_node(db_session)
        editor1 = EditorFixtures.create_editor_account(db_session, email="<EMAIL>")
        editor2 = EditorFixtures.create_editor_account(db_session, email="<EMAIL>")

        # Can assign editor to NEW draft
        draft = DraftFixtures.create_draft_exercise(db_session, learning_node=node)
        draft.assigned_editor_id = editor1.id
        draft.status = DraftExerciseStatus.IN_REVIEW
        db_session.commit()
        
        assert draft.assigned_editor_id == editor1.id
        assert draft.assigned_editor.email == "<EMAIL>"
        
        # Can reassign to different editor (for testing - in practice this would be controlled)
        draft.assigned_editor_id = editor2.id
        db_session.commit()
        assert draft.assigned_editor_id == editor2.id
    
    def test_draft_learning_node_relationship(self, db_session: Session):
        """Test draft has direct relationship with learning node"""
        # Create learning node
        node = LearningNodeFactory.create_node(
            node_type=LearningNodeTypeEnum.GRAMMAR.value,
            public_id=f"ln_{uuid.uuid4().hex[:8]}",
            title="Test Node",
            content={"video_public_id": None, "notes": "Test"}
        )
        db_session.add(node)
        db_session.commit()

        # Create draft with direct learning node relationship
        draft = DraftFixtures.create_draft_exercise(
            db_session,
            learning_node=node
        )

        # Test direct relationship
        assert draft.learning_node is not None
        assert draft.learning_node.id == node.id
        assert draft.learning_node.title == "Test Node"
        assert draft.learning_node_id == node.id
    
    def test_published_exercise_relationship(self, db_session: Session):
        """Test relationship with published exercise"""
        node = self._create_test_learning_node(db_session)

        # Create a published exercise
        from db.models import ExerciseTypeEnum, DifficultyEnum
        exercise = Exercise(
            public_id="ex_123",
            exercise_type=ExerciseTypeEnum.MC_SIMPLE,
            difficulty=DifficultyEnum.MEDIUM,
            _data={"prompt": "Published question"}
        )
        db_session.add(exercise)
        db_session.commit()

        # Create draft linked to published exercise
        draft = DraftFixtures.create_draft_exercise(
            db_session,
            learning_node=node,
            status=DraftExerciseStatus.PUBLISHED
        )
        draft.published_exercise_id = exercise.id
        draft.published_at = datetime.now(UTC)
        db_session.commit()
        
        assert draft.published_exercise_id == exercise.id
        assert draft.published_exercise.public_id == "ex_123"

