import pytest
from sqlalchemy.orm import Session
import uuid

from db.models import (
    EditorAccount, EditorScope, EditorRole,
    DraftExercise,
    Subject, Chapter, LearningNode, LearningNodeFactory, LearningNodeTypeEnum
)
from tests.fixtures.editorial_fixtures import EditorFixtures, DraftFixtures

class TestScopeBasedAuthorization:
    """Test scope-based authorization for editors"""
    
    def test_editor_can_access_drafts_in_subject_scope(self, db_session: Session):
        """Test editor can access drafts in their assigned subjects"""
        # Create editor with subject scope
        editor = EditorFixtures.create_editor_account(db_session)
        subject = Subject(name="Math", year_id=1)
        db_session.add(subject)
        db_session.commit()
        
        scope = EditorScope(editor_id=editor.id, subject_id=subject.id)
        db_session.add(scope)
        db_session.commit()
        
        # Create chapter and node in the subject
        chapter = Chapter(
            title="Algebra",
            subject_id=subject.id,
            ordering=1
        )
        db_session.add(chapter)
        db_session.commit()
        
        node = LearningNodeFactory.create_node(
            node_type=LearningNodeTypeEnum.MATH.value,
            public_id=f"ln_{uuid.uuid4().hex[:8]}",
            title="Linear Equations",
            content={"video_public_id": None, "notes": "Test"}
        )
        node.chapter_id = chapter.id
        db_session.add(node)
        db_session.commit()
        
        # Create draft associated with the node
        draft = DraftFixtures.create_draft_exercise(
            db_session,
            learning_node=node
        )
        
        # Verify editor has access through subject scope
        assert editor.scopes[0].subject_id == subject.id
        assert draft.learning_node.chapter.subject_id == subject.id
    
    def test_editor_can_access_drafts_in_chapter_scope(self, db_session: Session):
        """Test editor can access drafts in their assigned chapters"""
        editor = EditorFixtures.create_editor_account(db_session)
        
        # Create subject and chapter
        subject = Subject(name="Science", year_id=1)
        db_session.add(subject)
        db_session.commit()
        
        chapter = Chapter(
            title="Physics",
            subject_id=subject.id,
            ordering=1
        )
        db_session.add(chapter)
        db_session.commit()
        
        # Create scope at chapter level
        scope = EditorScope(editor_id=editor.id, chapter_id=chapter.id)
        db_session.add(scope)
        db_session.commit()
        
        # Create node in the chapter
        node = LearningNodeFactory.create_node(
            node_type=LearningNodeTypeEnum.READING.value,
            public_id=f"ln_{uuid.uuid4().hex[:8]}",
            title="Newton's Laws",
            content={"markdown_text": "Test content"}
        )
        node.chapter_id = chapter.id
        db_session.add(node)
        db_session.commit()
        
        # Create draft
        draft = DraftFixtures.create_draft_exercise(
            db_session,
            learning_node=node
        )
        
        # Verify access through chapter scope
        assert editor.scopes[0].chapter_id == chapter.id
        assert draft.learning_node.chapter_id == chapter.id
    
    def test_editor_can_access_drafts_in_node_scope(self, db_session: Session):
        """Test editor can access drafts in their assigned nodes"""
        editor = EditorFixtures.create_editor_account(db_session)
        
        # Create node
        node = LearningNodeFactory.create_node(
            node_type=LearningNodeTypeEnum.VOCABULARY.value,
            public_id=f"ln_{uuid.uuid4().hex[:8]}",
            title="Spanish Vocabulary",
            content={
                "base_language": "en",
                "target_language": "es",
                "groups": []
            }
        )
        db_session.add(node)
        db_session.commit()
        
        # Create scope at node level
        scope = EditorScope(editor_id=editor.id, learning_node_id=node.id)
        db_session.add(scope)
        db_session.commit()
        
        # Create draft
        draft = DraftFixtures.create_draft_exercise(
            db_session,
            learning_node=node
        )
        
        # Verify access through node scope
        assert editor.scopes[0].learning_node_id == node.id
        assert draft.learning_node.id == node.id
    
    def test_editor_cannot_access_drafts_outside_scope(self, db_session: Session):
        """Test editor cannot access drafts outside their scope"""
        editor = EditorFixtures.create_editor_account(db_session)
        
        # Create two subjects
        subject1 = Subject(name="Math", year_id=1)
        subject2 = Subject(name="Science", year_id=1)
        db_session.add_all([subject1, subject2])
        db_session.commit()
        
        # Editor has scope only for subject1
        scope = EditorScope(editor_id=editor.id, subject_id=subject1.id)
        db_session.add(scope)
        db_session.commit()
        
        # Create chapter and node in subject2 (outside scope)
        chapter2 = Chapter(
            title="Chemistry",
            subject_id=subject2.id,
            ordering=1
        )
        db_session.add(chapter2)
        db_session.commit()
        
        node2 = LearningNodeFactory.create_node(
            node_type=LearningNodeTypeEnum.READING.value,
            public_id=f"ln_{uuid.uuid4().hex[:8]}",
            title="Chemical Reactions",
            content={"markdown_text": "Test"}
        )
        node2.chapter_id = chapter2.id
        db_session.add(node2)
        db_session.commit()
        
        # Create draft in subject2
        draft = DraftFixtures.create_draft_exercise(
            db_session,
            learning_node=node2
        )
        
        # Verify editor scope doesn't include this draft
        editor_subject_ids = [s.subject_id for s in editor.scopes]
        draft_subject_id = draft.learning_node.chapter.subject_id

        assert draft_subject_id not in editor_subject_ids
    
    def test_admin_can_access_all_drafts(self, db_session: Session):
        """Test admin can access all drafts regardless of scope"""
        admin = EditorFixtures.create_editor_account(
            db_session,
            role=EditorRole.ADMIN
        )
        
        # Create drafts in different subjects
        subject1 = Subject(name="Math", year_id=1)
        subject2 = Subject(name="Science", year_id=1)
        db_session.add_all([subject1, subject2])
        db_session.commit()
        
        # Admin has no specific scopes
        assert len(admin.scopes) == 0
        
        # But admin role should allow access to all drafts
        assert admin.role == EditorRole.ADMIN
    
    def test_editor_with_multiple_scopes(self, db_session: Session):
        """Test editor with multiple scopes can access all assigned areas"""
        editor = EditorFixtures.create_editor_account(db_session)
        
        # Create multiple subjects
        math = Subject(name="Math", year_id=1)
        science = Subject(name="Science", year_id=1)
        db_session.add_all([math, science])
        db_session.commit()
        
        # Create chapters
        algebra = Chapter(title="Algebra", subject_id=math.id, ordering=1)
        physics = Chapter(title="Physics", subject_id=science.id, ordering=1)
        db_session.add_all([algebra, physics])
        db_session.commit()
        
        # Create multiple scopes
        scope1 = EditorScope(editor_id=editor.id, subject_id=math.id)
        scope2 = EditorScope(editor_id=editor.id, chapter_id=physics.id)
        db_session.add_all([scope1, scope2])
        db_session.commit()
        
        # Verify editor has both scopes
        assert len(editor.scopes) == 2
        
        # Check scope types
        scope_types = []
        for scope in editor.scopes:
            if scope.subject_id:
                scope_types.append("subject")
            elif scope.chapter_id:
                scope_types.append("chapter")
            elif scope.learning_node_id:
                scope_types.append("node")
        
        assert "subject" in scope_types
        assert "chapter" in scope_types
    
    def test_scope_hierarchy_enforcement(self, db_session: Session):
        """Test scope hierarchy is properly enforced"""
        editor = EditorFixtures.create_editor_account(db_session)
        
        # Create hierarchy
        subject = Subject(name="Language Arts", year_id=1)
        db_session.add(subject)
        db_session.commit()
        
        chapter = Chapter(
            title="Grammar",
            subject_id=subject.id,
            ordering=1
        )
        db_session.add(chapter)
        db_session.commit()
        
        node = LearningNodeFactory.create_node(
            node_type=LearningNodeTypeEnum.GRAMMAR.value,
            public_id=f"ln_{uuid.uuid4().hex[:8]}",
            title="Verb Tenses",
            content={"video_public_id": None, "notes": "Test"}
        )
        node.chapter_id = chapter.id
        db_session.add(node)
        db_session.commit()
        
        # If editor has subject scope, they can access everything below
        subject_scope = EditorScope(editor_id=editor.id, subject_id=subject.id)
        db_session.add(subject_scope)
        db_session.commit()
        
        # Create drafts at different levels
        draft_in_node = DraftFixtures.create_draft_exercise(
            db_session,
            learning_node=node
        )
        
        # Editor with subject scope should have access to draft in nested node
        assert editor.scopes[0].subject_id == subject.id
        assert draft_in_node.learning_node.chapter.subject_id == subject.id