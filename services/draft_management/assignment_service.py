from sqlalchemy.orm import Session
from sqlalchemy import select, and_
from typing import List, Optional, Dict, Any
from datetime import datetime, UTC, timedelta

from db.models import (
    DraftExercise, DraftExerciseStatus, EditorAccount, EditorRole,
    LearningNode
)
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ValidationError, PermissionDeniedError
)
from services.draft_management.scope_service import ScopeService
from api.v1.common.schemas import AppErrorCode
from loguru import logger

class AssignmentService:
    """Service for managing draft assignments to editors"""
    
    @staticmethod
    async def auto_assign_draft(
        db: Session,
        draft_id: int
    ) -> Optional[EditorAccount]:
        """
        Automatically assign a draft to an eligible editor.
        
        Selection criteria:
        1. Editor must have scope covering the draft
        2. Editor with fewest active assignments gets priority
        3. Only considers active editors
        """
        draft = db.get(DraftExercise, draft_id)
        if not draft:
            raise NotFoundError(
                message="Draft not found",
                entity_name="DraftExercise",
                identifier=draft_id
            )
        
        if draft.status != DraftExerciseStatus.NEW:
            raise ValidationError(
                message=f"Cannot auto-assign draft in status {draft.status.value}",
                error_code=AppErrorCode.INVALID_STATUS
            )
        
        # Find eligible editors
        eligible_editors = await AssignmentService._find_eligible_editors(db, draft)
        
        if not eligible_editors:
            logger.warning(f"No eligible editors found for draft {draft_id}")
            return None
        
        # Get workload for each editor
        editor_workloads = []
        for editor in eligible_editors:
            workload = await AssignmentService._get_editor_workload(db, editor)
            editor_workloads.append((editor, workload))
        
        # Sort by workload (ascending)
        editor_workloads.sort(key=lambda x: x[1])
        
        # Assign to editor with lowest workload
        selected_editor = editor_workloads[0][0]
        
        draft.assigned_editor_id = selected_editor.id
        draft.status = DraftExerciseStatus.IN_REVIEW
        draft.updated_at = datetime.now(UTC)
        

        
        db.commit()
        logger.info(f"Draft {draft_id} auto-assigned to editor {selected_editor.email}")
        
        return selected_editor
    
    @staticmethod
    async def reassign_draft(
        db: Session,
        draft_id: int,
        new_editor_id: int,
        admin: EditorAccount
    ) -> DraftExercise:
        """Reassign a draft to a different editor (admin only)"""
        if admin.role != EditorRole.ADMIN:
            raise PermissionDeniedError(
                message="Only admins can reassign drafts"
            )
        
        draft = db.get(DraftExercise, draft_id)
        if not draft:
            raise NotFoundError(
                message="Draft not found",
                entity_name="DraftExercise",
                identifier=draft_id
            )
        
        new_editor = db.get(EditorAccount, new_editor_id)
        if not new_editor or not new_editor.is_active:
            raise NotFoundError(
                message="New editor not found or inactive",
                entity_name="EditorAccount",
                identifier=new_editor_id
            )
        
        # Check if new editor has scope
        if not await ScopeService.check_editor_scope(db, new_editor, draft):
            raise ValidationError(
                message="New editor doesn't have scope for this draft",
                error_code=AppErrorCode.OUT_OF_SCOPE
            )
        
        old_editor_email = draft.assigned_editor.email if draft.assigned_editor else "unassigned"
        
        # Reassign
        draft.assigned_editor_id = new_editor_id
        draft.updated_at = datetime.now(UTC)
        

        
        db.commit()
        logger.info(
            f"Draft {draft_id} reassigned from {old_editor_email} "
            f"to {new_editor.email} by admin {admin.email}"
        )
        
        return draft
    
    @staticmethod
    async def release_draft(
        db: Session,
        draft_id: int,
        editor: EditorAccount
    ) -> DraftExercise:
        """Release a draft assignment (make it available for others)"""
        draft = db.get(DraftExercise, draft_id)
        if not draft:
            raise NotFoundError(
                message="Draft not found",
                entity_name="DraftExercise",
                identifier=draft_id
            )
        
        # Check ownership or admin
        if draft.assigned_editor_id != editor.id and editor.role != EditorRole.ADMIN:
            raise PermissionDeniedError(
                message="You cannot release this draft",
                error_code=AppErrorCode.NOT_ASSIGNED
            )
        
        if draft.status == DraftExerciseStatus.PUBLISHED:
            raise ValidationError(
                message="Cannot release a published draft",
                error_code=AppErrorCode.INVALID_STATUS
            )
        
        # Release the draft
        draft.assigned_editor_id = None
        draft.status = DraftExerciseStatus.NEW
        draft.updated_at = datetime.now(UTC)
        

        
        db.commit()
        logger.info(f"Draft {draft_id} released by {editor.email}")
        
        return draft
    
    @staticmethod
    async def _find_eligible_editors(
        db: Session,
        draft: DraftExercise
    ) -> List[EditorAccount]:
        """Find all editors who have scope to work on this draft"""
        # Get all active editors (not admins)
        stmt = select(EditorAccount).where(
            and_(
                EditorAccount.is_active == True,
                EditorAccount.role == EditorRole.EDITOR
            )
        )
        
        result = db.execute(stmt)
        all_editors = result.scalars().all()
        
        # Filter by scope
        eligible = []
        for editor in all_editors:
            if await ScopeService.check_editor_scope(db, editor, draft):
                eligible.append(editor)
        
        return eligible
    
    @staticmethod
    async def _get_editor_workload(
        db: Session,
        editor: EditorAccount
    ) -> int:
        """Get the number of active drafts assigned to an editor"""
        stmt = select(DraftExercise).where(
            and_(
                DraftExercise.assigned_editor_id == editor.id,
                DraftExercise.status.in_([
                    DraftExerciseStatus.IN_REVIEW,
                    DraftExerciseStatus.REJECTED_BY_ADMIN
                ])
            )
        )
        
        result = db.execute(stmt)
        active_drafts = result.scalars().all()
        
        return len(active_drafts)
    
    @staticmethod
    async def get_editor_assignments(
        db: Session,
        editor_id: int
    ) -> Dict[str, Any]:
        """Get assignment statistics for an editor"""
        editor = db.get(EditorAccount, editor_id)
        if not editor:
            raise NotFoundError(
                message="Editor not found",
                entity_name="EditorAccount",
                identifier=editor_id
            )
        
        # Get counts by status
        status_counts = {}
        for status in DraftExerciseStatus:
            count = db.query(DraftExercise).filter(
                DraftExercise.assigned_editor_id == editor_id,
                DraftExercise.status == status
            ).count()
            status_counts[status.value] = count
        
        # Get recent activity
        recent_drafts = db.query(DraftExercise).filter(
            DraftExercise.assigned_editor_id == editor_id
        ).order_by(DraftExercise.updated_at.desc()).limit(10).all()
        
        return {
            "editor_id": editor.id,
            "editor_email": editor.email,
            "total_assigned": sum(status_counts.values()),
            "status_breakdown": status_counts,
            "active_count": status_counts.get(DraftExerciseStatus.IN_REVIEW.value, 0) + 
                           status_counts.get(DraftExerciseStatus.REJECTED_BY_ADMIN.value, 0),
            "recent_drafts": [
                {
                    "id": d.id,
                    "public_id": d.public_id,
                    "status": d.status.value,
                    "updated_at": d.updated_at.isoformat() + "Z"
                }
                for d in recent_drafts
            ]
        }